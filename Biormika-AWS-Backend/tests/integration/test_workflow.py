import pytest
import json
import os
from unittest.mock import patch, MagicMock
from analysis.core.hfo_analysis import run_hfo_detection
from analysis.tasks import read_edf_file, detect_hfo, generate_visualization, process_results

@pytest.mark.integration
class TestHFOAnalysisWorkflow:
    
    def test_hfo_detection_algorithm(self, sample_edf_data, analysis_params):
        results = run_hfo_detection(
            sample_edf_data['data'],
            sample_edf_data['sampling_rate'],
            analysis_params
        )
        
        assert 'channels' in results
        assert 'total_hfos' in results
        assert 'hfo_rate' in results
        assert 'analysis_duration' in results
        assert len(results['channels']) == sample_edf_data['num_channels']
    
    @patch('analysis.tasks.boto3')
    def test_read_edf_file_handler(self, mock_boto3, s3_bucket, sample_edf_data):
        mock_s3 = MagicMock()
        mock_boto3.client.return_value = mock_s3
        
        mock_s3.get_object.return_value = {
            'Body': MagicMock(read=lambda: json.dumps({
                'data': sample_edf_data['data'].tolist(),
                'sampling_rate': sample_edf_data['sampling_rate']
            }).encode())
        }
        
        event = {
            'file_id': 'test-file-123',
            'bucket': s3_bucket,
            'key': 'files/test.edf',
            'user_id': 'test-user',
            'analysis_params': {}
        }
        
        response = read_edf_file(event, {})
        
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert 'edf_data' in body
        assert 'channels' in body
    
    @patch('analysis.tasks.boto3')
    def test_detect_hfo_handler(self, mock_boto3, sample_edf_data, analysis_params):
        event = {
            'edf_data': sample_edf_data['data'].tolist(),
            'sampling_rate': sample_edf_data['sampling_rate'],
            'analysis_params': analysis_params,
            'file_id': 'test-file-123',
            'user_id': 'test-user'
        }
        
        response = detect_hfo(event, {})
        
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert 'hfo_results' in body
        assert 'total_hfos' in body['hfo_results']
    
    @patch('visualization.plotter.generate_hfo_plot')
    @patch('analysis.tasks.boto3')
    def test_generate_visualization_handler(self, mock_boto3, mock_plot, sample_edf_data):
        mock_plot.return_value = b'mock_plot_data'
        
        event = {
            'hfo_results': {
                'channels': [],
                'total_hfos': 0,
                'hfo_rate': 0,
                'all_events': []
            },
            'edf_data': sample_edf_data['data'].tolist(),
            'sampling_rate': sample_edf_data['sampling_rate'],
            'file_id': 'test-file-123',
            'user_id': 'test-user'
        }
        
        response = generate_visualization(event, {})
        
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert 'visualization_urls' in body
    
    @patch('visualization.pdf_generator.generate_report')
    @patch('analysis.tasks.boto3')
    def test_process_results_handler(self, mock_boto3, mock_pdf):
        mock_pdf.return_value = b'mock_pdf_data'
        
        event = {
            'hfo_results': {
                'channels': [],
                'total_hfos': 5,
                'hfo_rate': 0.5,
                'all_events': []
            },
            'visualization_urls': ['https://example.com/plot1.png'],
            'file_id': 'test-file-123',
            'user_id': 'test-user',
            'file_metadata': {
                'filename': 'test.edf',
                'uploaded_at': '2025-01-18T12:00:00Z'
            }
        }
        
        response = process_results(event, {})
        
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert 'report_url' in body
        assert 'analysis_complete' in body
        assert body['analysis_complete'] is True
    
    @pytest.mark.slow
    @patch('analysis.tasks.boto3')
    def test_complete_workflow(self, mock_boto3, sample_edf_data, analysis_params):
        mock_s3 = MagicMock()
        mock_dynamodb = MagicMock()
        
        def mock_client(service):
            if service == 's3':
                return mock_s3
            elif service == 'dynamodb':
                return mock_dynamodb
            return MagicMock()
        
        mock_boto3.client.side_effect = mock_client
        mock_boto3.resource.return_value = mock_dynamodb
        
        mock_s3.get_object.return_value = {
            'Body': MagicMock(read=lambda: json.dumps({
                'data': sample_edf_data['data'].tolist(),
                'sampling_rate': sample_edf_data['sampling_rate']
            }).encode())
        }
        
        initial_event = {
            'file_id': 'test-file-123',
            'bucket': 'test-bucket',
            'key': 'files/test.edf',
            'user_id': 'test-user',
            'analysis_params': analysis_params
        }
        
        step1_response = read_edf_file(initial_event, {})
        assert step1_response['statusCode'] == 200
        step1_output = json.loads(step1_response['body'])
        
        step2_event = {**initial_event, **step1_output}
        step2_response = detect_hfo(step2_event, {})
        assert step2_response['statusCode'] == 200
        step2_output = json.loads(step2_response['body'])
        
        assert 'hfo_results' in step2_output
        assert step2_output['hfo_results']['total_hfos'] >= 0