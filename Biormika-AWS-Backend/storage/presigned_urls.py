import json
import os
import boto3
import uuid
from datetime import datetime
from botocore.exceptions import ClientError

s3 = boto3.client('s3')
S3_BUCKET = os.environ.get('S3_BUCKET')

def create_response(status_code, body):
    return {
        'statusCode': status_code,
        'headers': {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Credentials': True,
        },
        'body': json.dumps(body)
    }

def generate_upload_url(file_name, file_type, user_id, expiration=3600):
    try:
        timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
        file_id = str(uuid.uuid4())
        
        file_extension = os.path.splitext(file_name)[1]
        s3_key = f"users/{user_id}/uploads/{timestamp}_{file_id}{file_extension}"
        
        conditions = [
            {"bucket": S3_BUCKET},
            ["starts-with", "$key", f"users/{user_id}/uploads/"],
            {"success_action_status": "201"},
            ["content-length-range", 0, 104857600]  # Max 100MB
        ]
        
        if file_type:
            conditions.append({"Content-Type": file_type})
        
        presigned_post = s3.generate_presigned_post(
            Bucket=S3_BUCKET,
            Key=s3_key,
            Fields={
                "success_action_status": "201",
                "Content-Type": file_type,
                "x-amz-meta-user-id": user_id,
                "x-amz-meta-file-id": file_id,
                "x-amz-meta-original-name": file_name
            },
            Conditions=conditions,
            ExpiresIn=expiration
        )
        
        return {
            'uploadUrl': presigned_post['url'],
            'fields': presigned_post['fields'],
            'fileId': file_id,
            's3Key': s3_key,
            'expires': expiration
        }
        
    except ClientError as e:
        print(f"Error generating presigned URL: {e}")
        raise e

def generate_download_url(s3_key, expiration=3600):
    try:
        presigned_url = s3.generate_presigned_url(
            'get_object',
            Params={
                'Bucket': S3_BUCKET,
                'Key': s3_key
            },
            ExpiresIn=expiration
        )
        
        return presigned_url
        
    except ClientError as e:
        print(f"Error generating download URL: {e}")
        raise e

def delete_file(s3_key):
    try:
        s3.delete_object(
            Bucket=S3_BUCKET,
            Key=s3_key
        )
        return True
        
    except ClientError as e:
        print(f"Error deleting file: {e}")
        return False

def check_file_exists(s3_key):
    try:
        s3.head_object(
            Bucket=S3_BUCKET,
            Key=s3_key
        )
        return True
        
    except ClientError as e:
        if e.response['Error']['Code'] == '404':
            return False
        raise e

def get_file_metadata(s3_key):
    try:
        response = s3.head_object(
            Bucket=S3_BUCKET,
            Key=s3_key
        )
        
        return {
            'size': response['ContentLength'],
            'contentType': response.get('ContentType', 'application/octet-stream'),
            'lastModified': response['LastModified'].isoformat(),
            'metadata': response.get('Metadata', {})
        }
        
    except ClientError as e:
        if e.response['Error']['Code'] == '404':
            return None
        raise e