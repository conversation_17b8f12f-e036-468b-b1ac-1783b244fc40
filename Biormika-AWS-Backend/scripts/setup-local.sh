#!/bin/bash

echo "🚀 Setting up Biormika local development environment..."

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Check if docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}Docker is not running. Please start Docker first.${NC}"
    exit 1
fi

# Start LocalStack
echo -e "${YELLOW}Starting LocalStack services...${NC}"
docker-compose up -d

# Wait for LocalStack to be ready
echo -e "${YELLOW}Waiting for LocalStack to be ready...${NC}"
sleep 10

# Configure AWS CLI for local development
echo -e "${YELLOW}Configuring AWS CLI for local development...${NC}"
aws configure set aws_access_key_id test --profile local
aws configure set aws_secret_access_key test --profile local
aws configure set region us-east-1 --profile local

# Create S3 bucket
BUCKET_NAME="biormika-hfo-backend-local-files"
echo -e "${YELLOW}Creating S3 bucket: ${BUCKET_NAME}${NC}"
aws --endpoint-url=http://localhost:4566 s3 mb s3://${BUCKET_NAME} --profile local

# Configure S3 bucket CORS
echo -e "${YELLOW}Configuring S3 bucket CORS...${NC}"
cat > /tmp/cors.json << EOF
{
  "CORSRules": [
    {
      "AllowedOrigins": ["*"],
      "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
      "AllowedHeaders": ["*"],
      "ExposeHeaders": ["ETag"],
      "MaxAgeSeconds": 3000
    }
  ]
}
EOF

aws --endpoint-url=http://localhost:4566 s3api put-bucket-cors \
    --bucket ${BUCKET_NAME} \
    --cors-configuration file:///tmp/cors.json \
    --profile local

# Create DynamoDB table
TABLE_NAME="biormika-hfo-backend-local"
echo -e "${YELLOW}Creating DynamoDB table: ${TABLE_NAME}${NC}"
aws --endpoint-url=http://localhost:4566 dynamodb create-table \
    --table-name ${TABLE_NAME} \
    --attribute-definitions \
        AttributeName=PK,AttributeType=S \
        AttributeName=SK,AttributeType=S \
    --key-schema \
        AttributeName=PK,KeyType=HASH \
        AttributeName=SK,KeyType=RANGE \
    --billing-mode PAY_PER_REQUEST \
    --profile local

# Create Cognito User Pool (mock)
echo -e "${YELLOW}Creating mock Cognito User Pool...${NC}"
aws --endpoint-url=http://localhost:4566 cognito-idp create-user-pool \
    --pool-name biormika-local-pool \
    --profile local > /dev/null 2>&1

# Install Python dependencies
echo -e "${YELLOW}Installing Python dependencies...${NC}"
pip install -r requirements.txt

# Copy .env.local if it doesn't exist
if [ ! -f .env ]; then
    cp .env.local .env
    echo -e "${GREEN}Created .env from .env.local${NC}"
fi

echo -e "${GREEN}✅ Local development environment setup complete!${NC}"
echo -e "${GREEN}You can now run: npm run offline${NC}"
echo -e "${GREEN}DynamoDB Admin UI: http://localhost:8001${NC}"