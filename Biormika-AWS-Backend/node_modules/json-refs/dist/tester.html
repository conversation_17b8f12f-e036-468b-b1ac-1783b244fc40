<html>
  <head>
    <title>JsonRefs Test Page</title>
  </head>
  <body>
    <p>Resolving references in the following:</p>
    <pre>{
  $ref: 'https://api.github.com/repos/whitlockjc/json-refs'
}</pre>
  </body>
  <script src="json-refs.js"></script>
  <script>
    JsonRefs.resolveRefs({
      $ref: 'https://api.github.com/repos/whitlockjc/json-refs'
    })
    .then(function (resolved) {
      document.body.innerHTML += '<p><strong>Dereferenced Content</strong></p><pre>' + JSON.stringify(resolved, null, 2) + '</pre>'
    })
    .catch(function (err) {
      document.body.innerHTML += '<p><strong>Dereferencing Failed</strong></p><span>' + err.message + '</span>';
    });
  </script>
</html>
