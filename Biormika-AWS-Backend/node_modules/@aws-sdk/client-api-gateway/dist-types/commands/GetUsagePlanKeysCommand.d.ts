import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { APIGatewayClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes } from "../APIGatewayClient";
import { GetUsagePlanKeysRequest, UsagePlanKeys } from "../models/models_0";
/**
 * @public
 */
export type { __MetadataBearer };
export { $Command };
/**
 * @public
 *
 * The input for {@link GetUsagePlanKeysCommand}.
 */
export interface GetUsagePlanKeysCommandInput extends GetUsagePlanKeysRequest {
}
/**
 * @public
 *
 * The output of {@link GetUsagePlanKeysCommand}.
 */
export interface GetUsagePlanKeysCommandOutput extends UsagePlanKeys, __MetadataBearer {
}
declare const GetUsagePlanKeysCommand_base: {
    new (input: GetUsagePlanKeysCommandInput): import("@smithy/smithy-client").CommandImpl<GetUsagePlanKeysCommandInput, GetUsagePlanKeysCommandOutput, APIGatewayClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    new (input: GetUsagePlanKeysCommandInput): import("@smithy/smithy-client").CommandImpl<GetUsagePlanKeysCommandInput, GetUsagePlanKeysCommandOutput, APIGatewayClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
/**
 * <p>Gets all the usage plan keys representing the API keys added to a specified usage plan.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { APIGatewayClient, GetUsagePlanKeysCommand } from "@aws-sdk/client-api-gateway"; // ES Modules import
 * // const { APIGatewayClient, GetUsagePlanKeysCommand } = require("@aws-sdk/client-api-gateway"); // CommonJS import
 * const client = new APIGatewayClient(config);
 * const input = { // GetUsagePlanKeysRequest
 *   usagePlanId: "STRING_VALUE", // required
 *   position: "STRING_VALUE",
 *   limit: Number("int"),
 *   nameQuery: "STRING_VALUE",
 * };
 * const command = new GetUsagePlanKeysCommand(input);
 * const response = await client.send(command);
 * // { // UsagePlanKeys
 * //   items: [ // ListOfUsagePlanKey
 * //     { // UsagePlanKey
 * //       id: "STRING_VALUE",
 * //       type: "STRING_VALUE",
 * //       value: "STRING_VALUE",
 * //       name: "STRING_VALUE",
 * //     },
 * //   ],
 * //   position: "STRING_VALUE",
 * // };
 *
 * ```
 *
 * @param GetUsagePlanKeysCommandInput - {@link GetUsagePlanKeysCommandInput}
 * @returns {@link GetUsagePlanKeysCommandOutput}
 * @see {@link GetUsagePlanKeysCommandInput} for command's `input` shape.
 * @see {@link GetUsagePlanKeysCommandOutput} for command's `response` shape.
 * @see {@link APIGatewayClientResolvedConfig | config} for APIGatewayClient's `config` shape.
 *
 * @throws {@link BadRequestException} (client fault)
 *  <p>The submitted request is not valid, for example, the input is incomplete or incorrect. See the accompanying error message for details.</p>
 *
 * @throws {@link NotFoundException} (client fault)
 *  <p>The requested resource is not found. Make sure that the request URI is correct.</p>
 *
 * @throws {@link TooManyRequestsException} (client fault)
 *  <p>The request has reached its throttling limit. Retry after the specified time period.</p>
 *
 * @throws {@link UnauthorizedException} (client fault)
 *  <p>The request is denied because the caller has insufficient permissions.</p>
 *
 * @throws {@link APIGatewayServiceException}
 * <p>Base exception class for all service exceptions from APIGateway service.</p>
 *
 *
 * @public
 */
export declare class GetUsagePlanKeysCommand extends GetUsagePlanKeysCommand_base {
    /** @internal type navigation helper, not in runtime. */
    protected static __types: {
        api: {
            input: GetUsagePlanKeysRequest;
            output: UsagePlanKeys;
        };
        sdk: {
            input: GetUsagePlanKeysCommandInput;
            output: GetUsagePlanKeysCommandOutput;
        };
    };
}
