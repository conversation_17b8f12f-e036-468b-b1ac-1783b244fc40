import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { APIGatewayClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes } from "../APIGatewayClient";
import { GetUsagePlansRequest, UsagePlans } from "../models/models_0";
/**
 * @public
 */
export type { __MetadataBearer };
export { $Command };
/**
 * @public
 *
 * The input for {@link GetUsagePlansCommand}.
 */
export interface GetUsagePlansCommandInput extends GetUsagePlansRequest {
}
/**
 * @public
 *
 * The output of {@link GetUsagePlansCommand}.
 */
export interface GetUsagePlansCommandOutput extends UsagePlans, __MetadataBearer {
}
declare const GetUsagePlansCommand_base: {
    new (input: GetUsagePlansCommandInput): import("@smithy/smithy-client").CommandImpl<GetUsagePlansCommandInput, GetUsagePlansCommandOutput, APIGatewayClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    new (...[input]: [] | [GetUsagePlansCommandInput]): import("@smithy/smithy-client").CommandImpl<GetUsagePlansCommandInput, GetUsagePlansCommandOutput, APIGatewayClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
/**
 * <p>Gets all the usage plans of the caller's account.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { APIGatewayClient, GetUsagePlansCommand } from "@aws-sdk/client-api-gateway"; // ES Modules import
 * // const { APIGatewayClient, GetUsagePlansCommand } = require("@aws-sdk/client-api-gateway"); // CommonJS import
 * const client = new APIGatewayClient(config);
 * const input = { // GetUsagePlansRequest
 *   position: "STRING_VALUE",
 *   keyId: "STRING_VALUE",
 *   limit: Number("int"),
 * };
 * const command = new GetUsagePlansCommand(input);
 * const response = await client.send(command);
 * // { // UsagePlans
 * //   items: [ // ListOfUsagePlan
 * //     { // UsagePlan
 * //       id: "STRING_VALUE",
 * //       name: "STRING_VALUE",
 * //       description: "STRING_VALUE",
 * //       apiStages: [ // ListOfApiStage
 * //         { // ApiStage
 * //           apiId: "STRING_VALUE",
 * //           stage: "STRING_VALUE",
 * //           throttle: { // MapOfApiStageThrottleSettings
 * //             "<keys>": { // ThrottleSettings
 * //               burstLimit: Number("int"),
 * //               rateLimit: Number("double"),
 * //             },
 * //           },
 * //         },
 * //       ],
 * //       throttle: {
 * //         burstLimit: Number("int"),
 * //         rateLimit: Number("double"),
 * //       },
 * //       quota: { // QuotaSettings
 * //         limit: Number("int"),
 * //         offset: Number("int"),
 * //         period: "DAY" || "WEEK" || "MONTH",
 * //       },
 * //       productCode: "STRING_VALUE",
 * //       tags: { // MapOfStringToString
 * //         "<keys>": "STRING_VALUE",
 * //       },
 * //     },
 * //   ],
 * //   position: "STRING_VALUE",
 * // };
 *
 * ```
 *
 * @param GetUsagePlansCommandInput - {@link GetUsagePlansCommandInput}
 * @returns {@link GetUsagePlansCommandOutput}
 * @see {@link GetUsagePlansCommandInput} for command's `input` shape.
 * @see {@link GetUsagePlansCommandOutput} for command's `response` shape.
 * @see {@link APIGatewayClientResolvedConfig | config} for APIGatewayClient's `config` shape.
 *
 * @throws {@link BadRequestException} (client fault)
 *  <p>The submitted request is not valid, for example, the input is incomplete or incorrect. See the accompanying error message for details.</p>
 *
 * @throws {@link NotFoundException} (client fault)
 *  <p>The requested resource is not found. Make sure that the request URI is correct.</p>
 *
 * @throws {@link TooManyRequestsException} (client fault)
 *  <p>The request has reached its throttling limit. Retry after the specified time period.</p>
 *
 * @throws {@link UnauthorizedException} (client fault)
 *  <p>The request is denied because the caller has insufficient permissions.</p>
 *
 * @throws {@link APIGatewayServiceException}
 * <p>Base exception class for all service exceptions from APIGateway service.</p>
 *
 *
 * @public
 */
export declare class GetUsagePlansCommand extends GetUsagePlansCommand_base {
    /** @internal type navigation helper, not in runtime. */
    protected static __types: {
        api: {
            input: GetUsagePlansRequest;
            output: UsagePlans;
        };
        sdk: {
            input: GetUsagePlansCommandInput;
            output: GetUsagePlansCommandOutput;
        };
    };
}
