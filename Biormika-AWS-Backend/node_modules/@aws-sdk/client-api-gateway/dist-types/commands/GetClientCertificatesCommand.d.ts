import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { APIGatewayClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes } from "../APIGatewayClient";
import { ClientCertificates, GetClientCertificatesRequest } from "../models/models_0";
/**
 * @public
 */
export type { __MetadataBearer };
export { $Command };
/**
 * @public
 *
 * The input for {@link GetClientCertificatesCommand}.
 */
export interface GetClientCertificatesCommandInput extends GetClientCertificatesRequest {
}
/**
 * @public
 *
 * The output of {@link GetClientCertificatesCommand}.
 */
export interface GetClientCertificatesCommandOutput extends ClientCertificates, __MetadataBearer {
}
declare const GetClientCertificatesCommand_base: {
    new (input: GetClientCertificatesCommandInput): import("@smithy/smithy-client").CommandImpl<GetClientCertificatesCommandInput, GetClientCertificatesCommandOutput, APIGatewayClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    new (...[input]: [] | [GetClientCertificatesCommandInput]): import("@smithy/smithy-client").CommandImpl<GetClientCertificatesCommandInput, GetClientCertificatesCommandOutput, APIGatewayClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
/**
 * <p>Gets a collection of ClientCertificate resources.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { APIGatewayClient, GetClientCertificatesCommand } from "@aws-sdk/client-api-gateway"; // ES Modules import
 * // const { APIGatewayClient, GetClientCertificatesCommand } = require("@aws-sdk/client-api-gateway"); // CommonJS import
 * const client = new APIGatewayClient(config);
 * const input = { // GetClientCertificatesRequest
 *   position: "STRING_VALUE",
 *   limit: Number("int"),
 * };
 * const command = new GetClientCertificatesCommand(input);
 * const response = await client.send(command);
 * // { // ClientCertificates
 * //   items: [ // ListOfClientCertificate
 * //     { // ClientCertificate
 * //       clientCertificateId: "STRING_VALUE",
 * //       description: "STRING_VALUE",
 * //       pemEncodedCertificate: "STRING_VALUE",
 * //       createdDate: new Date("TIMESTAMP"),
 * //       expirationDate: new Date("TIMESTAMP"),
 * //       tags: { // MapOfStringToString
 * //         "<keys>": "STRING_VALUE",
 * //       },
 * //     },
 * //   ],
 * //   position: "STRING_VALUE",
 * // };
 *
 * ```
 *
 * @param GetClientCertificatesCommandInput - {@link GetClientCertificatesCommandInput}
 * @returns {@link GetClientCertificatesCommandOutput}
 * @see {@link GetClientCertificatesCommandInput} for command's `input` shape.
 * @see {@link GetClientCertificatesCommandOutput} for command's `response` shape.
 * @see {@link APIGatewayClientResolvedConfig | config} for APIGatewayClient's `config` shape.
 *
 * @throws {@link BadRequestException} (client fault)
 *  <p>The submitted request is not valid, for example, the input is incomplete or incorrect. See the accompanying error message for details.</p>
 *
 * @throws {@link NotFoundException} (client fault)
 *  <p>The requested resource is not found. Make sure that the request URI is correct.</p>
 *
 * @throws {@link TooManyRequestsException} (client fault)
 *  <p>The request has reached its throttling limit. Retry after the specified time period.</p>
 *
 * @throws {@link UnauthorizedException} (client fault)
 *  <p>The request is denied because the caller has insufficient permissions.</p>
 *
 * @throws {@link APIGatewayServiceException}
 * <p>Base exception class for all service exceptions from APIGateway service.</p>
 *
 *
 * @public
 */
export declare class GetClientCertificatesCommand extends GetClientCertificatesCommand_base {
    /** @internal type navigation helper, not in runtime. */
    protected static __types: {
        api: {
            input: GetClientCertificatesRequest;
            output: ClientCertificates;
        };
        sdk: {
            input: GetClientCertificatesCommandInput;
            output: GetClientCertificatesCommandOutput;
        };
    };
}
