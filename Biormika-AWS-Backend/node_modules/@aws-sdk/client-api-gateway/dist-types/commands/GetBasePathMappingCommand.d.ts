import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { APIGatewayClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes } from "../APIGatewayClient";
import { BasePathMapping, GetBasePathMappingRequest } from "../models/models_0";
/**
 * @public
 */
export type { __MetadataBearer };
export { $Command };
/**
 * @public
 *
 * The input for {@link GetBasePathMappingCommand}.
 */
export interface GetBasePathMappingCommandInput extends GetBasePathMappingRequest {
}
/**
 * @public
 *
 * The output of {@link GetBasePathMappingCommand}.
 */
export interface GetBasePathMappingCommandOutput extends BasePathMapping, __MetadataBearer {
}
declare const GetBasePathMappingCommand_base: {
    new (input: GetBasePathMappingCommandInput): import("@smithy/smithy-client").CommandImpl<GetBasePathMappingCommandInput, GetBasePathMappingCommandOutput, APIGatewayClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    new (input: GetBasePathMappingCommandInput): import("@smithy/smithy-client").CommandImpl<GetBasePathMappingCommandInput, GetBasePathMappingCommandOutput, APIGatewayClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
/**
 * <p>Describe a BasePathMapping resource.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { APIGatewayClient, GetBasePathMappingCommand } from "@aws-sdk/client-api-gateway"; // ES Modules import
 * // const { APIGatewayClient, GetBasePathMappingCommand } = require("@aws-sdk/client-api-gateway"); // CommonJS import
 * const client = new APIGatewayClient(config);
 * const input = { // GetBasePathMappingRequest
 *   domainName: "STRING_VALUE", // required
 *   domainNameId: "STRING_VALUE",
 *   basePath: "STRING_VALUE", // required
 * };
 * const command = new GetBasePathMappingCommand(input);
 * const response = await client.send(command);
 * // { // BasePathMapping
 * //   basePath: "STRING_VALUE",
 * //   restApiId: "STRING_VALUE",
 * //   stage: "STRING_VALUE",
 * // };
 *
 * ```
 *
 * @param GetBasePathMappingCommandInput - {@link GetBasePathMappingCommandInput}
 * @returns {@link GetBasePathMappingCommandOutput}
 * @see {@link GetBasePathMappingCommandInput} for command's `input` shape.
 * @see {@link GetBasePathMappingCommandOutput} for command's `response` shape.
 * @see {@link APIGatewayClientResolvedConfig | config} for APIGatewayClient's `config` shape.
 *
 * @throws {@link BadRequestException} (client fault)
 *  <p>The submitted request is not valid, for example, the input is incomplete or incorrect. See the accompanying error message for details.</p>
 *
 * @throws {@link NotFoundException} (client fault)
 *  <p>The requested resource is not found. Make sure that the request URI is correct.</p>
 *
 * @throws {@link TooManyRequestsException} (client fault)
 *  <p>The request has reached its throttling limit. Retry after the specified time period.</p>
 *
 * @throws {@link UnauthorizedException} (client fault)
 *  <p>The request is denied because the caller has insufficient permissions.</p>
 *
 * @throws {@link APIGatewayServiceException}
 * <p>Base exception class for all service exceptions from APIGateway service.</p>
 *
 *
 * @public
 */
export declare class GetBasePathMappingCommand extends GetBasePathMappingCommand_base {
    /** @internal type navigation helper, not in runtime. */
    protected static __types: {
        api: {
            input: GetBasePathMappingRequest;
            output: BasePathMapping;
        };
        sdk: {
            input: GetBasePathMappingCommandInput;
            output: GetBasePathMappingCommandOutput;
        };
    };
}
