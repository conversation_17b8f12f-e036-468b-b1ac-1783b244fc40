import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { APIGatewayClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes } from "../APIGatewayClient";
import { DocumentationVersion, GetDocumentationVersionRequest } from "../models/models_0";
/**
 * @public
 */
export type { __MetadataBearer };
export { $Command };
/**
 * @public
 *
 * The input for {@link GetDocumentationVersionCommand}.
 */
export interface GetDocumentationVersionCommandInput extends GetDocumentationVersionRequest {
}
/**
 * @public
 *
 * The output of {@link GetDocumentationVersionCommand}.
 */
export interface GetDocumentationVersionCommandOutput extends DocumentationVersion, __MetadataBearer {
}
declare const GetDocumentationVersionCommand_base: {
    new (input: GetDocumentationVersionCommandInput): import("@smithy/smithy-client").CommandImpl<GetDocumentationVersionCommandInput, GetDocumentationVersionCommandOutput, APIGatewayClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    new (input: GetDocumentationVersionCommandInput): import("@smithy/smithy-client").CommandImpl<GetDocumentationVersionCommandInput, GetDocumentationVersionCommandOutput, APIGatewayClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
/**
 * <p>Gets a documentation version.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { APIGatewayClient, GetDocumentationVersionCommand } from "@aws-sdk/client-api-gateway"; // ES Modules import
 * // const { APIGatewayClient, GetDocumentationVersionCommand } = require("@aws-sdk/client-api-gateway"); // CommonJS import
 * const client = new APIGatewayClient(config);
 * const input = { // GetDocumentationVersionRequest
 *   restApiId: "STRING_VALUE", // required
 *   documentationVersion: "STRING_VALUE", // required
 * };
 * const command = new GetDocumentationVersionCommand(input);
 * const response = await client.send(command);
 * // { // DocumentationVersion
 * //   version: "STRING_VALUE",
 * //   createdDate: new Date("TIMESTAMP"),
 * //   description: "STRING_VALUE",
 * // };
 *
 * ```
 *
 * @param GetDocumentationVersionCommandInput - {@link GetDocumentationVersionCommandInput}
 * @returns {@link GetDocumentationVersionCommandOutput}
 * @see {@link GetDocumentationVersionCommandInput} for command's `input` shape.
 * @see {@link GetDocumentationVersionCommandOutput} for command's `response` shape.
 * @see {@link APIGatewayClientResolvedConfig | config} for APIGatewayClient's `config` shape.
 *
 * @throws {@link NotFoundException} (client fault)
 *  <p>The requested resource is not found. Make sure that the request URI is correct.</p>
 *
 * @throws {@link TooManyRequestsException} (client fault)
 *  <p>The request has reached its throttling limit. Retry after the specified time period.</p>
 *
 * @throws {@link UnauthorizedException} (client fault)
 *  <p>The request is denied because the caller has insufficient permissions.</p>
 *
 * @throws {@link APIGatewayServiceException}
 * <p>Base exception class for all service exceptions from APIGateway service.</p>
 *
 *
 * @public
 */
export declare class GetDocumentationVersionCommand extends GetDocumentationVersionCommand_base {
    /** @internal type navigation helper, not in runtime. */
    protected static __types: {
        api: {
            input: GetDocumentationVersionRequest;
            output: DocumentationVersion;
        };
        sdk: {
            input: GetDocumentationVersionCommandInput;
            output: GetDocumentationVersionCommandOutput;
        };
    };
}
