import json
import traceback
from functools import wraps

def create_error_response(status_code, message, error_type=None):
    return {
        'statusCode': status_code,
        'headers': {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Credentials': True,
        },
        'body': json.dumps({
            'success': False,
            'message': message,
            'error': error_type,
            'statusCode': status_code
        })
    }

def handle_errors(handler):
    @wraps(handler)
    def wrapper(event, context):
        try:
            return handler(event, context)
        except ValueError as e:
            print(f"ValueError in {handler.__name__}: {str(e)}")
            return create_error_response(400, str(e), 'ValidationError')
        except KeyError as e:
            print(f"KeyError in {handler.__name__}: {str(e)}")
            return create_error_response(400, f"Missing required field: {str(e)}", 'ValidationError')
        except PermissionError as e:
            print(f"PermissionError in {handler.__name__}: {str(e)}")
            return create_error_response(403, 'Access denied', 'PermissionError')
        except FileNotFoundError as e:
            print(f"FileNotFoundError in {handler.__name__}: {str(e)}")
            return create_error_response(404, 'Resource not found', 'NotFoundError')
        except TimeoutError as e:
            print(f"TimeoutError in {handler.__name__}: {str(e)}")
            return create_error_response(504, 'Request timeout', 'TimeoutError')
        except Exception as e:
            print(f"Unexpected error in {handler.__name__}: {str(e)}")
            print(traceback.format_exc())
            return create_error_response(500, 'Internal server error', 'InternalError')
    
    return wrapper

class ApplicationError(Exception):
    def __init__(self, message, status_code=400, error_type=None):
        self.message = message
        self.status_code = status_code
        self.error_type = error_type
        super().__init__(self.message)

class ValidationError(ApplicationError):
    def __init__(self, message):
        super().__init__(message, 400, 'ValidationError')

class AuthenticationError(ApplicationError):
    def __init__(self, message='Authentication required'):
        super().__init__(message, 401, 'AuthenticationError')

class AuthorizationError(ApplicationError):
    def __init__(self, message='Access denied'):
        super().__init__(message, 403, 'AuthorizationError')

class NotFoundError(ApplicationError):
    def __init__(self, message='Resource not found'):
        super().__init__(message, 404, 'NotFoundError')

class ConflictError(ApplicationError):
    def __init__(self, message='Resource conflict'):
        super().__init__(message, 409, 'ConflictError')

class RateLimitError(ApplicationError):
    def __init__(self, message='Rate limit exceeded'):
        super().__init__(message, 429, 'RateLimitError')

class ServiceError(ApplicationError):
    def __init__(self, message='Service unavailable'):
        super().__init__(message, 503, 'ServiceError')