import json
import logging
import os
from datetime import datetime

LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
SERVICE_NAME = os.environ.get('SERVICE_NAME', 'biormika-hfo-backend')
STAGE = os.environ.get('STAGE', 'dev')

class JSONFormatter(logging.Formatter):
    def format(self, record):
        log_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'service': SERVICE_NAME,
            'stage': STAGE,
            'message': record.getMessage(),
            'logger': record.name,
            'function': record.funcName,
            'line': record.lineno,
            'path': record.pathname
        }
        
        if hasattr(record, 'user_id'):
            log_data['user_id'] = record.user_id
        
        if hasattr(record, 'analysis_id'):
            log_data['analysis_id'] = record.analysis_id
        
        if hasattr(record, 'request_id'):
            log_data['request_id'] = record.request_id
        
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)
        
        return json.dumps(log_data)

def get_logger(name=None):
    logger = logging.getLogger(name or __name__)
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        handler.setFormatter(JSONFormatter())
        logger.addHandler(handler)
        logger.setLevel(getattr(logging, LOG_LEVEL))
    
    return logger

def log_lambda_event(logger, event, context):
    request_context = event.get('requestContext', {})
    
    log_data = {
        'request_id': context.request_id if context else 'unknown',
        'function_name': context.function_name if context else 'unknown',
        'function_version': context.function_version if context else 'unknown',
        'memory_limit': context.memory_limit_in_mb if context else 'unknown',
        'remaining_time': context.get_remaining_time_in_millis() if context else 'unknown',
        'path': event.get('path'),
        'http_method': event.get('httpMethod'),
        'source_ip': request_context.get('identity', {}).get('sourceIp'),
        'user_agent': request_context.get('identity', {}).get('userAgent'),
        'api_id': request_context.get('apiId'),
        'stage': request_context.get('stage')
    }
    
    logger.info('Lambda invoked', extra={'lambda_context': log_data})
    
    return log_data['request_id']

def log_performance(logger, operation, duration_ms, metadata=None):
    log_data = {
        'operation': operation,
        'duration_ms': duration_ms,
        'performance': True
    }
    
    if metadata:
        log_data.update(metadata)
    
    logger.info(f'Performance: {operation}', extra=log_data)

def log_error(logger, error, operation=None, metadata=None):
    log_data = {
        'error_type': type(error).__name__,
        'error_message': str(error),
        'operation': operation
    }
    
    if metadata:
        log_data.update(metadata)
    
    logger.error(f'Error in {operation or "operation"}', exc_info=error, extra=log_data)

def log_audit(logger, action, resource, user_id, result='success', metadata=None):
    log_data = {
        'audit': True,
        'action': action,
        'resource': resource,
        'user_id': user_id,
        'result': result,
        'timestamp': datetime.utcnow().isoformat()
    }
    
    if metadata:
        log_data.update(metadata)
    
    logger.info(f'Audit: {action} on {resource}', extra=log_data)