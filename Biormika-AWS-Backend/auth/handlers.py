import json
import os
import boto3
import bcrypt
from jose import jwt, JW<PERSON>rror
from datetime import datetime, timedelta
from botocore.exceptions import ClientError

cognito = boto3.client('cognito-idp')
USER_POOL_ID = os.environ.get('COGNITO_USER_POOL_ID')
CLIENT_ID = os.environ.get('COGNITO_CLIENT_ID')
JWT_SECRET = os.environ.get('JWT_SECRET', 'your-secret-key')
JWT_ALGORITHM = 'HS256'
JWT_EXPIRATION_HOURS = 24

def create_response(status_code, body):
    return {
        'statusCode': status_code,
        'headers': {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Credentials': True,
        },
        'body': json.dumps(body)
    }

def generate_jwt_token(user_data):
    expiration = datetime.utcnow() + timedelta(hours=JWT_EXPIRATION_HOURS)
    payload = {
        'sub': user_data['id'],
        'email': user_data['email'],
        'name': user_data['name'],
        'role': user_data.get('role', 'user'),
        'exp': expiration
    }
    return jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)

def login(event, context):
    try:
        body = json.loads(event['body'])
        email = body.get('email')
        password = body.get('password')
        
        if not email or not password:
            return create_response(400, {
                'success': False,
                'message': 'Email and password are required',
                'statusCode': 400
            })
        
        try:
            response = cognito.admin_initiate_auth(
                UserPoolId=USER_POOL_ID,
                ClientId=CLIENT_ID,
                AuthFlow='ADMIN_NO_SRP_AUTH',
                AuthParameters={
                    'USERNAME': email,
                    'PASSWORD': password
                }
            )
            
            user_response = cognito.admin_get_user(
                UserPoolId=USER_POOL_ID,
                Username=email
            )
            
            user_attributes = {attr['Name']: attr['Value'] for attr in user_response['UserAttributes']}
            
            user_data = {
                'id': user_attributes.get('sub'),
                'email': user_attributes.get('email'),
                'name': user_attributes.get('name', ''),
                'role': user_attributes.get('custom:role', 'user'),
                'permissions': ['read', 'write']
            }
            
            token = generate_jwt_token(user_data)
            
            return create_response(200, {
                'success': True,
                'data': {
                    'user': user_data,
                    'token': token
                },
                'message': 'Login successful',
                'statusCode': 200
            })
            
        except cognito.exceptions.UserNotFoundException:
            return create_response(404, {
                'success': False,
                'message': 'User not found',
                'statusCode': 404
            })
        except cognito.exceptions.NotAuthorizedException:
            return create_response(401, {
                'success': False,
                'message': 'Invalid credentials',
                'statusCode': 401
            })
        except ClientError as e:
            print(f"Cognito error: {e}")
            return create_response(500, {
                'success': False,
                'message': 'Authentication service error',
                'statusCode': 500
            })
            
    except json.JSONDecodeError:
        return create_response(400, {
            'success': False,
            'message': 'Invalid request body',
            'statusCode': 400
        })
    except Exception as e:
        print(f"Login error: {str(e)}")
        return create_response(500, {
            'success': False,
            'message': 'Internal server error',
            'statusCode': 500
        })

def signup(event, context):
    try:
        body = json.loads(event['body'])
        email = body.get('email')
        password = body.get('password')
        full_name = body.get('fullName')
        
        if not email or not password or not full_name:
            return create_response(400, {
                'success': False,
                'message': 'Email, password, and full name are required',
                'statusCode': 400
            })
        
        try:
            response = cognito.admin_create_user(
                UserPoolId=USER_POOL_ID,
                Username=email,
                UserAttributes=[
                    {'Name': 'email', 'Value': email},
                    {'Name': 'name', 'Value': full_name},
                    {'Name': 'email_verified', 'Value': 'true'}
                ],
                TemporaryPassword=password,
                MessageAction='SUPPRESS'
            )
            
            cognito.admin_set_user_password(
                UserPoolId=USER_POOL_ID,
                Username=email,
                Password=password,
                Permanent=True
            )
            
            user_data = {
                'id': response['User']['Username'],
                'email': email,
                'name': full_name,
                'role': 'user',
                'permissions': ['read', 'write']
            }
            
            token = generate_jwt_token(user_data)
            
            return create_response(201, {
                'success': True,
                'data': {
                    'user': user_data,
                    'token': token
                },
                'message': 'User created successfully',
                'statusCode': 201
            })
            
        except cognito.exceptions.UsernameExistsException:
            return create_response(409, {
                'success': False,
                'message': 'User already exists',
                'statusCode': 409
            })
        except ClientError as e:
            print(f"Cognito signup error: {e}")
            return create_response(500, {
                'success': False,
                'message': 'Failed to create user',
                'statusCode': 500
            })
            
    except json.JSONDecodeError:
        return create_response(400, {
            'success': False,
            'message': 'Invalid request body',
            'statusCode': 400
        })
    except Exception as e:
        print(f"Signup error: {str(e)}")
        return create_response(500, {
            'success': False,
            'message': 'Internal server error',
            'statusCode': 500
        })

def refresh_token(event, context):
    try:
        body = json.loads(event['body'])
        refresh_token = body.get('refreshToken')
        
        if not refresh_token:
            return create_response(400, {
                'success': False,
                'message': 'Refresh token is required',
                'statusCode': 400
            })
        
        try:
            response = cognito.admin_initiate_auth(
                UserPoolId=USER_POOL_ID,
                ClientId=CLIENT_ID,
                AuthFlow='REFRESH_TOKEN_AUTH',
                AuthParameters={
                    'REFRESH_TOKEN': refresh_token
                }
            )
            
            id_token = response['AuthenticationResult']['IdToken']
            access_token = response['AuthenticationResult']['AccessToken']
            
            return create_response(200, {
                'success': True,
                'data': {
                    'idToken': id_token,
                    'accessToken': access_token
                },
                'message': 'Token refreshed successfully',
                'statusCode': 200
            })
            
        except ClientError as e:
            print(f"Token refresh error: {e}")
            return create_response(401, {
                'success': False,
                'message': 'Invalid refresh token',
                'statusCode': 401
            })
            
    except json.JSONDecodeError:
        return create_response(400, {
            'success': False,
            'message': 'Invalid request body',
            'statusCode': 400
        })
    except Exception as e:
        print(f"Refresh token error: {str(e)}")
        return create_response(500, {
            'success': False,
            'message': 'Internal server error',
            'statusCode': 500
        })

def verify_token(token):
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        return payload
    except JWTError:
        return None