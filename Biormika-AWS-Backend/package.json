{"name": "biormika-aws-backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "./scripts/run-tests.sh", "test:unit": "pytest tests/unit -v", "test:integration": "pytest tests/integration -v", "test:coverage": "pytest --cov=. --cov-report=html tests/", "offline": "serverless offline start", "deploy:dev": "serverless deploy --stage dev", "deploy:prod": "serverless deploy --stage prod", "setup:local": "./scripts/setup-local.sh"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"serverless": "^3.40.0", "serverless-dotenv-plugin": "^6.0.0", "serverless-offline": "^13.9.0", "serverless-python-requirements": "^6.1.2"}}