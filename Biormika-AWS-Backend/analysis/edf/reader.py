import numpy as np
import re
import boto3
import io
import os

s3 = boto3.client('s3')
S3_BUCKET = os.environ.get('S3_BUCKET')

def read_edf_from_s3(s3_key, target_signals=None):
    try:
        response = s3.get_object(Bucket=S3_BUCKET, Key=s3_key)
        file_content = response['Body'].read()
        
        return parse_edf_content(file_content, target_signals)
        
    except Exception as e:
        print(f"Error reading EDF from S3: {e}")
        raise e

def parse_edf_content(file_content, target_signals=None):
    fid = io.BytesIO(file_content)
    
    hdr = {}
    hdr['ver'] = int(fid.read(8).decode('ascii').strip())
    hdr['patientID'] = fid.read(80).decode('ascii').strip()
    hdr['recordID'] = fid.read(80).decode('ascii').strip()
    hdr['startdate'] = fid.read(8).decode('ascii').strip()
    hdr['starttime'] = fid.read(8).decode('ascii').strip()
    hdr['bytes'] = int(fid.read(8).decode('ascii').strip())
    fid.read(44)  # Reserved
    hdr['records'] = int(fid.read(8).decode('ascii').strip())
    hdr['duration'] = float(fid.read(8).decode('ascii').strip())
    hdr['ns'] = int(fid.read(4).decode('ascii').strip())
    
    hdr['label'] = [re.sub(r'\W+$', '', fid.read(16).decode('ascii').strip()) for _ in range(hdr['ns'])]
    hdr['transducer'] = [fid.read(80).decode('ascii').strip() for _ in range(hdr['ns'])]
    hdr['units'] = [fid.read(8).decode('ascii').strip() for _ in range(hdr['ns'])]
    hdr['physicalMin'] = np.array([float(fid.read(8).decode('ascii').strip()) for _ in range(hdr['ns'])])
    hdr['physicalMax'] = np.array([float(fid.read(8).decode('ascii').strip()) for _ in range(hdr['ns'])])
    hdr['digitalMin'] = np.array([int(fid.read(8).decode('ascii').strip()) for _ in range(hdr['ns'])])
    hdr['digitalMax'] = np.array([int(fid.read(8).decode('ascii').strip()) for _ in range(hdr['ns'])])
    hdr['prefilter'] = [fid.read(80).decode('ascii').strip() for _ in range(hdr['ns'])]
    hdr['samples'] = np.array([int(fid.read(8).decode('ascii').strip()) for _ in range(hdr['ns'])])
    
    for _ in range(hdr['ns']):
        fid.read(32)  # Reserved
    
    hdr['frequency'] = hdr['samples'] / hdr['duration']
    
    if target_signals is None:
        target_signals = list(range(hdr['ns']))
    elif isinstance(target_signals, (str, list)):
        if isinstance(target_signals, str):
            target_signals = [target_signals]
        target_signals = [i for i, label in enumerate(hdr['label']) if label in target_signals]
        if not target_signals:
            raise ValueError("The requested signals were not detected.")
    else:
        raise ValueError("Invalid targetSignals parameter.")
    
    hdr['label'] = [hdr['label'][i] for i in target_signals]
    hdr['units'] = [hdr['units'][i] for i in target_signals]
    hdr['physicalMin'] = hdr['physicalMin'][target_signals]
    hdr['physicalMax'] = hdr['physicalMax'][target_signals]
    hdr['digitalMin'] = hdr['digitalMin'][target_signals]
    hdr['digitalMax'] = hdr['digitalMax'][target_signals]
    hdr['prefilter'] = [hdr['prefilter'][i] for i in target_signals]
    hdr['transducer'] = [hdr['transducer'][i] for i in target_signals]
    hdr['samples'] = hdr['samples'][target_signals]
    
    scalefac = (hdr['physicalMax'] - hdr['physicalMin']) / (hdr['digitalMax'] - hdr['digitalMin'])
    dc = hdr['physicalMax'] - scalefac * hdr['digitalMax']
    
    record = []
    
    for _ in range(hdr['records']):
        rec = []
        for i in range(hdr['ns']):
            if i in target_signals:
                samples = hdr['samples'][i]
                data = np.frombuffer(fid.read(samples * 2), dtype=np.int16, count=samples)
                scale_index = target_signals.index(i)
                data = data * scalefac[scale_index] + dc[scale_index]
                rec.append(data)
            else:
                fid.seek(hdr['samples'][i] * 2, 1)
        record.append(rec)
    
    record = np.array(record)
    record = record.swapaxes(0, 1).reshape(len(target_signals), -1)
    
    return hdr, record

def extract_channels_for_montage(hdr, record, montage_type, selected_channels=None):
    if montage_type == "bipolar":
        return create_bipolar_montage(hdr, record, selected_channels)
    elif montage_type == "average":
        return create_average_montage(hdr, record, selected_channels)
    elif montage_type == "referential":
        return create_referential_montage(hdr, record, selected_channels)
    else:
        return hdr, record

def create_bipolar_montage(hdr, record, selected_pairs):
    if not selected_pairs:
        return hdr, record
    
    new_labels = []
    new_data = []
    
    for pair in selected_pairs:
        ch1_name, ch2_name = pair.split('-')
        ch1_idx = hdr['label'].index(ch1_name.strip())
        ch2_idx = hdr['label'].index(ch2_name.strip())
        
        new_labels.append(pair)
        new_data.append(record[ch1_idx] - record[ch2_idx])
    
    new_hdr = hdr.copy()
    new_hdr['label'] = new_labels
    new_hdr['ns'] = len(new_labels)
    
    return new_hdr, np.array(new_data)

def create_average_montage(hdr, record, selected_channels):
    if not selected_channels:
        selected_channels = hdr['label']
    
    selected_indices = [i for i, label in enumerate(hdr['label']) if label in selected_channels]
    avg_signal = np.mean(record[selected_indices], axis=0)
    
    new_data = []
    new_labels = []
    
    for idx in selected_indices:
        new_data.append(record[idx] - avg_signal)
        new_labels.append(f"{hdr['label'][idx]}-AVG")
    
    new_hdr = hdr.copy()
    new_hdr['label'] = new_labels
    new_hdr['ns'] = len(new_labels)
    
    return new_hdr, np.array(new_data)

def create_referential_montage(hdr, record, reference_channel):
    if not reference_channel:
        return hdr, record
    
    ref_idx = hdr['label'].index(reference_channel)
    ref_signal = record[ref_idx]
    
    new_data = []
    new_labels = []
    
    for i, label in enumerate(hdr['label']):
        if i != ref_idx:
            new_data.append(record[i] - ref_signal)
            new_labels.append(f"{label}-{reference_channel}")
    
    new_hdr = hdr.copy()
    new_hdr['label'] = new_labels
    new_hdr['ns'] = len(new_labels)
    
    return new_hdr, np.array(new_data)