import json
import os
import boto3
from datetime import datetime

dynamodb = boto3.resource('dynamodb')
TABLE_NAME = os.environ.get('DYNAMODB_TABLE')
table = dynamodb.Table(TABLE_NAME) if TABLE_NAME else None

apigateway = boto3.client('apigatewaymanagementapi',
    endpoint_url=os.environ.get('WEBSOCKET_ENDPOINT', 'https://localhost'))

def create_response(status_code, body=None):
    response = {
        'statusCode': status_code,
        'headers': {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Credentials': True,
        }
    }
    
    if body:
        response['body'] = json.dumps(body)
    
    return response

def connect(event, context):
    try:
        connection_id = event['requestContext']['connectionId']
        
        if table:
            table.put_item(
                Item={
                    'PK': 'CONNECTION',
                    'SK': f'CONN#{connection_id}',
                    'connectionId': connection_id,
                    'connectedAt': datetime.utcnow().isoformat(),
                    'ttl': int(datetime.utcnow().timestamp() + 3600)
                }
            )
        
        return create_response(200, {'message': 'Connected'})
        
    except Exception as e:
        print(f"Connection error: {e}")
        return create_response(500, {'message': 'Failed to connect'})

def disconnect(event, context):
    try:
        connection_id = event['requestContext']['connectionId']
        
        if table:
            table.delete_item(
                Key={
                    'PK': 'CONNECTION',
                    'SK': f'CONN#{connection_id}'
                }
            )
            
            response = table.query(
                IndexName='GSI1',
                KeyConditionExpression='GSI1PK = :pk AND GSI1SK = :sk',
                ExpressionAttributeValues={
                    ':pk': f'CONN#{connection_id}',
                    ':sk': 'SUBSCRIPTION'
                }
            )
            
            for item in response.get('Items', []):
                table.delete_item(
                    Key={
                        'PK': item['PK'],
                        'SK': item['SK']
                    }
                )
        
        return create_response(200, {'message': 'Disconnected'})
        
    except Exception as e:
        print(f"Disconnection error: {e}")
        return create_response(500, {'message': 'Failed to disconnect'})

def default(event, context):
    try:
        connection_id = event['requestContext']['connectionId']
        body = json.loads(event.get('body', '{}'))
        
        action = body.get('action')
        
        if action == 'subscribe':
            return handle_subscribe(connection_id, body)
        elif action == 'unsubscribe':
            return handle_unsubscribe(connection_id, body)
        elif action == 'ping':
            return handle_ping(connection_id)
        else:
            return create_response(400, {'message': 'Unknown action'})
            
    except Exception as e:
        print(f"Default handler error: {e}")
        return create_response(500, {'message': 'Internal server error'})

def handle_subscribe(connection_id, body):
    try:
        analysis_id = body.get('analysisId')
        user_id = body.get('userId')
        
        if not analysis_id or not user_id:
            return create_response(400, {'message': 'analysisId and userId required'})
        
        if table:
            table.put_item(
                Item={
                    'PK': f'ANALYSIS#{analysis_id}',
                    'SK': f'SUBSCRIPTION#{connection_id}',
                    'GSI1PK': f'CONN#{connection_id}',
                    'GSI1SK': 'SUBSCRIPTION',
                    'connectionId': connection_id,
                    'analysisId': analysis_id,
                    'userId': user_id,
                    'subscribedAt': datetime.utcnow().isoformat(),
                    'ttl': int(datetime.utcnow().timestamp() + 3600)
                }
            )
        
        send_message(connection_id, {
            'type': 'subscribed',
            'analysisId': analysis_id,
            'message': 'Successfully subscribed to analysis updates'
        })
        
        return create_response(200, {'message': 'Subscribed'})
        
    except Exception as e:
        print(f"Subscribe error: {e}")
        return create_response(500, {'message': 'Failed to subscribe'})

def handle_unsubscribe(connection_id, body):
    try:
        analysis_id = body.get('analysisId')
        
        if not analysis_id:
            return create_response(400, {'message': 'analysisId required'})
        
        if table:
            table.delete_item(
                Key={
                    'PK': f'ANALYSIS#{analysis_id}',
                    'SK': f'SUBSCRIPTION#{connection_id}'
                }
            )
        
        send_message(connection_id, {
            'type': 'unsubscribed',
            'analysisId': analysis_id,
            'message': 'Successfully unsubscribed from analysis updates'
        })
        
        return create_response(200, {'message': 'Unsubscribed'})
        
    except Exception as e:
        print(f"Unsubscribe error: {e}")
        return create_response(500, {'message': 'Failed to unsubscribe'})

def handle_ping(connection_id):
    try:
        send_message(connection_id, {
            'type': 'pong',
            'timestamp': datetime.utcnow().isoformat()
        })
        
        return create_response(200, {'message': 'Pong'})
        
    except Exception as e:
        print(f"Ping error: {e}")
        return create_response(500, {'message': 'Failed to send pong'})

def send_message(connection_id, message):
    try:
        apigateway.post_to_connection(
            ConnectionId=connection_id,
            Data=json.dumps(message)
        )
    except apigateway.exceptions.GoneException:
        print(f"Connection {connection_id} is gone, cleaning up")
        if table:
            table.delete_item(
                Key={
                    'PK': 'CONNECTION',
                    'SK': f'CONN#{connection_id}'
                }
            )
    except Exception as e:
        print(f"Error sending message: {e}")

def broadcast_analysis_update(analysis_id, update_data):
    if not table:
        return
    
    try:
        response = table.query(
            KeyConditionExpression='PK = :pk AND begins_with(SK, :sk)',
            ExpressionAttributeValues={
                ':pk': f'ANALYSIS#{analysis_id}',
                ':sk': 'SUBSCRIPTION#'
            }
        )
        
        for item in response.get('Items', []):
            connection_id = item['connectionId']
            
            message = {
                'type': 'analysis_update',
                'analysisId': analysis_id,
                'update': update_data,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            send_message(connection_id, message)
            
    except Exception as e:
        print(f"Broadcast error: {e}")