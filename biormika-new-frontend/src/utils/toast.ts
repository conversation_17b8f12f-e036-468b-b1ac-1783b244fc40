import { toast } from 'sonner'
import type { ExternalToast } from 'sonner'

// Toast utility functions with TypeScript support
export const showToast = {
  success: (message: string, options?: ExternalToast) => {
    return toast.success(message, options)
  },

  error: (message: string, options?: ExternalToast) => {
    return toast.error(message, options)
  },

  warning: (message: string, options?: ExternalToast) => {
    return toast.warning(message, options)
  },

  info: (message: string, options?: ExternalToast) => {
    return toast.info(message, options)
  },

  loading: (message: string, options?: ExternalToast) => {
    return toast.loading(message, options)
  },

  default: (message: string, options?: ExternalToast) => {
    return toast(message, options)
  },

  // Promise toast for async operations
  promise: <T>(
    promise: Promise<T>,
    {
      loading,
      success,
      error,
    }: {
      loading: string
      success: string | ((data: T) => string)
      error: string | ((error: any) => string)
    }
  ) => {
    return toast.promise(promise, { loading, success, error })
  },

  // Custom toast with JSX
  custom: (jsx: (id: string | number) => React.ReactElement, options?: ExternalToast) => {
    return toast.custom(jsx, options)
  },

  // Dismiss toasts
  dismiss: (id?: string | number) => {
    if (id) {
      toast.dismiss(id)
    } else {
      toast.dismiss()
    }
  },
}

// Convenience functions for common use cases
export const notifySuccess = (message: string) => showToast.success(message)
export const notifyError = (message: string) => showToast.error(message)
export const notifyWarning = (message: string) => showToast.warning(message)
export const notifyInfo = (message: string) => showToast.info(message)

// API response toast helpers
export const handleApiSuccess = (message: string = 'Operation completed successfully') => {
  showToast.success(message)
}

export const handleApiError = (error: any) => {
  const message = error?.response?.data?.message || error?.message || 'An error occurred'
  showToast.error(message)
}

// Promise-based operation helpers
export const withToast = <T>(
  promise: Promise<T>,
  messages: {
    loading?: string
    success?: string | ((data: T) => string)
    error?: string | ((error: any) => string)
  } = {}
): Promise<T> => {
  const {
    loading = 'Loading...',
    success = 'Success!',
    error = 'Something went wrong',
  } = messages

  return new Promise((resolve, reject) => {
    showToast.promise(
      promise,
      {
        loading,
        success,
        error,
      }
    )

    promise
      .then(resolve)
      .catch(reject)
  })
}

// Export the base toast function
export { toast }
export default showToast