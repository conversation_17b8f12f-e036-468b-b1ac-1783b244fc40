export const isDevelopment = process.env.NODE_ENV === 'development'
export const isProduction = process.env.NODE_ENV === 'production'

export const logger = {
  log: (...args: any[]) => {
    if (isDevelopment) {
      console.log(...args)
    }
  },
  warn: (...args: any[]) => {
    if (isDevelopment) {
      console.warn(...args)
    }
  },
  error: (...args: any[]) => {
    console.error(...args)
  },
}

export const debugRedux = (state: any, action?: any) => {
  if (isDevelopment) {
    console.group('Redux Debug')
    console.log('State:', state)
    if (action) console.log('Action:', action)
    console.groupEnd()
  }
}