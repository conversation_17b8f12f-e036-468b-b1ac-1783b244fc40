import type { AxiosResponse, AxiosError } from 'axios'

// Custom Axios response type
export interface ApiAxiosResponse<T = any> extends AxiosResponse<T> {
  data: T
}

// Custom Axios error type
export interface ApiAxiosError<T = any> extends AxiosError<T> {
  response?: AxiosResponse<T>
}

// Request configuration
export interface RequestConfig {
  showSuccessToast?: boolean
  showErrorToast?: boolean
  successMessage?: string
  skipAuthRefresh?: boolean
}

// Interceptor types
export interface RequestInterceptorConfig {
  onRequest?: (config: any) => any
  onRequestError?: (error: any) => Promise<any>
}

export interface ResponseInterceptorConfig {
  onResponse?: (response: ApiAxiosResponse) => ApiAxiosResponse
  onResponseError?: (error: ApiAxiosError) => Promise<any>
}