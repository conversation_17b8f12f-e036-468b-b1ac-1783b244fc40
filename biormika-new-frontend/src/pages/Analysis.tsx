import { PageLayout } from '../components/PageLayout'
import { PageHeader } from '../components/PageHeader'
import { LoadingSpinner } from '../components/LoadingSpinner'
import { useAppSelector } from '../store'
import { brandColors } from '../constants/colors'

export default function Analysis() {
  const { files } = useAppSelector(state => state.files)
  const { globalSettings } = useAppSelector(state => state.analysisSettings)

  return (
    <PageLayout>
      <div className="w-full max-w-4xl mx-auto">
        <PageHeader 
          title="Analysis in Progress"
          subtitle="Processing your EDF file with the configured parameters. This may take a few minutes."
          variant="processing"
        >
          <LoadingSpinner />
        </PageHeader>

        {/* File Information */}
        <div className="bg-white rounded-lg p-6 mb-6 border" style={{ borderColor: brandColors.border.light }}>
          <h3 className="text-lg font-semibold mb-4" style={{ color: brandColors.text.primary }}>
            File Being Analyzed
          </h3>
          {files.length > 0 && (
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <p className="font-medium" style={{ color: brandColors.text.primary }}>
                  {files[0].name}
                </p>
                <p className="text-sm" style={{ color: brandColors.text.secondary }}>
                  Size: {(files[0].size / (1024 * 1024)).toFixed(2)} MB
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm" style={{ color: brandColors.text.secondary }}>
                  Status: Processing
                </p>
                <p className="text-sm" style={{ color: brandColors.text.secondary }}>
                  Started: {new Date().toLocaleTimeString()}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Analysis Parameters Summary */}
        {globalSettings && (
          <div className="bg-white rounded-lg p-6 mb-6 border" style={{ borderColor: brandColors.border.light }}>
            <h3 className="text-lg font-semibold mb-4" style={{ color: brandColors.text.primary }}>
              Analysis Parameters
            </h3>
            <div className="grid grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-2" style={{ color: brandColors.text.secondary }}>
                  Threshold Settings
                </h4>
                <ul className="text-sm space-y-1" style={{ color: brandColors.text.muted }}>
                  <li>Amplitude 1: {globalSettings.thresholdSettings.amplitude1}</li>
                  <li>Amplitude 2: {globalSettings.thresholdSettings.amplitude2}</li>
                  <li>Peaks 1: {globalSettings.thresholdSettings.peaks1}</li>
                  <li>Peaks 2: {globalSettings.thresholdSettings.peaks2}</li>
                  <li>Duration: {globalSettings.thresholdSettings.duration} ms</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2" style={{ color: brandColors.text.secondary }}>
                  Frequency Filters
                </h4>
                <ul className="text-sm space-y-1" style={{ color: brandColors.text.muted }}>
                  <li>Low Cutoff: {globalSettings.frequencyFilterSettings.lowCutoffFilter} Hz</li>
                  <li>High Cutoff: {globalSettings.frequencyFilterSettings.highCutoffFilter} Hz</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2" style={{ color: brandColors.text.secondary }}>
                  Synchronization
                </h4>
                <ul className="text-sm space-y-1" style={{ color: brandColors.text.muted }}>
                  <li>Temporal Sync: {globalSettings.synchronizationSettings.temporalSync} ms</li>
                  <li>Spatial Sync: {globalSettings.synchronizationSettings.spatialSync} ms</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2" style={{ color: brandColors.text.secondary }}>
                  Montage Selection
                </h4>
                <ul className="text-sm space-y-1" style={{ color: brandColors.text.muted }}>
                  <li>Bipolar: {globalSettings.montageSelection.bipolar ? 'Yes' : 'No'}</li>
                  <li>Average: {globalSettings.montageSelection.average ? 'Yes' : 'No'}</li>
                  <li>Referential: {globalSettings.montageSelection.referential ? 'Yes' : 'No'}</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Progress Indicator */}
        <div className="bg-white rounded-lg p-6 border" style={{ borderColor: brandColors.border.light }}>
          <h3 className="text-lg font-semibold mb-4" style={{ color: brandColors.text.primary }}>
            Processing Status
          </h3>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="w-4 h-4 rounded-full" style={{ backgroundColor: brandColors.success }}></div>
              <span style={{ color: brandColors.text.primary }}>File validation completed</span>
            </div>
            <div className="flex items-center gap-3">
              <LoadingSpinner />
              <span style={{ color: brandColors.text.primary }}>Running HFO detection algorithm...</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-4 h-4 rounded-full border-2" style={{ borderColor: brandColors.border.medium }}></div>
              <span style={{ color: brandColors.text.muted }}>Generating analysis report</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-4 h-4 rounded-full border-2" style={{ borderColor: brandColors.border.medium }}></div>
              <span style={{ color: brandColors.text.muted }}>Preparing results for download</span>
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  )
}