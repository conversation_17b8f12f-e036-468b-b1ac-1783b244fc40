import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { toast } from 'sonner'
import { FileList } from '../components/FileList'
import { FileUploadArea } from '../components/FileUploadArea'
import { LoadingSpinner } from '../components/LoadingSpinner'
import { PageHeader } from '../components/PageHeader'
import { PageLayout } from '../components/PageLayout'
import { SettingsSidebar } from '../components/SettingsSidebar'
import { useAppDispatch, useAppSelector } from '../store'
import {
  addFiles,
  clearAllFiles,
  setIsUploading,
  updateFileProgress,
  updateFileStatus,
} from '../store/slices/filesSlice'
import { FileStatus } from '../types/files'
import { validateFiles, checkDuplicateFiles } from '../utils/fileValidation'

export default function Dashboard() {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [selectedFileId, setSelectedFileId] = useState<string>('')
  const [selectedFileName, setSelectedFileName] = useState<string>('')
  
  const dispatch = useAppDispatch()
  const navigate = useNavigate()
  const { files, isUploading } = useAppSelector((state) => state.files)
  const { isConfigured } = useAppSelector((state) => state.analysisSettings)
  const hasFiles = files.length > 0

  useEffect(() => {
    if (files.length === 0 || !isUploading) {
      return
    }

    const simulateUpload = async () => {
      for (const file of files) {
        if (file.status === FileStatus.PENDING) {
          dispatch(updateFileStatus({ id: file.id, status: FileStatus.UPLOADING }))

          for (let progress = 0; progress <= 100; progress += Math.random() * 20) {
            await new Promise((resolve) => setTimeout(resolve, 100))
            dispatch(updateFileProgress({ id: file.id, progress: Math.min(progress, 100) }))
          }

          dispatch(updateFileStatus({ id: file.id, status: FileStatus.VALID }))
        }
      }

      dispatch(setIsUploading(false))
    }

    simulateUpload()
  }, [files, isUploading, dispatch])

  const handleReUpload = () => {
    dispatch(clearAllFiles())
  }

  const handleAddMoreFiles = () => {
    // Trigger the file browser directly instead of navigation
    document.getElementById('file-input')?.click()
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const fileList = Array.from(e.target.files || [])
    if (fileList.length > 0) {
      // Check for duplicates
      const { duplicates, unique } = checkDuplicateFiles(fileList, files)
      
      // Validate unique files
      const validFiles = validateFiles(unique)
      
      if (validFiles.length > 0) {
        dispatch(addFiles({ files: validFiles }))
        dispatch(setIsUploading(true))
        
        // Show success toast
        let message = `${validFiles.length} file${validFiles.length > 1 ? 's' : ''} added`
        if (duplicates.length > 0) {
          message += `, ${duplicates.length} duplicate${duplicates.length > 1 ? 's' : ''} skipped`
        }
        toast.success(message)
      } else if (duplicates.length > 0) {
        // Only duplicates were selected
        toast.error(`${duplicates.length} duplicate file${duplicates.length > 1 ? 's' : ''} skipped`)
      }
    }
    
    // Clear the input value so the same file can be selected again
    e.target.value = ''
  }

  const handleContinue = () => {
    if (isConfigured) {
      navigate('/analysis')
    } else {
      navigate('/configuration')
    }
  }

  const handleConfigure = (fileId: string) => {
    const file = files.find(f => f.id === fileId)
    if (file) {
      setSelectedFileId(fileId)
      setSelectedFileName(file.name)
      setSidebarOpen(true)
    }
  }

  const handleCloseSidebar = () => {
    setSidebarOpen(false)
    setSelectedFileId('')
    setSelectedFileName('')
  }

  const allFilesProcessed = files.every(
    (file) => file.status === FileStatus.VALID || file.status === FileStatus.ERROR
  )

  // Show initial upload screen when no files
  if (!hasFiles) {
    return (
      <PageLayout showFooter>
        <PageHeader title="Biormika" subtitle="Smarter Brainwave Analysis" variant="brand" />
        <FileUploadArea />
      </PageLayout>
    )
  }

  // Show processing screen when files are being uploaded
  if (isUploading) {
    return (
      <PageLayout>
        <div className="w-full max-w-4xl">
          <PageHeader
            title="Processing Files"
            subtitle="This may take a few minutes"
            variant="processing"
          >
            <LoadingSpinner />
          </PageHeader>

          <FileList showActions={false} hideHeader={true} onConfigure={handleConfigure} />
        </div>
      </PageLayout>
    )
  }

  // Show completed file management screen
  return (
    <>
      <PageLayout showFooter>
        <div className="w-full max-w-4xl">
          {/* Hidden file input for Add More Files functionality */}
          <input
            id="file-input"
            type="file"
            accept=".edf"
            multiple
            onChange={handleFileSelect}
            className="hidden"
          />
          
          <FileList
            showActions={true}
            hideHeader={false}
            onReUpload={allFilesProcessed ? handleReUpload : undefined}
            onAddMoreFiles={allFilesProcessed ? handleAddMoreFiles : undefined}
            onContinue={allFilesProcessed ? handleContinue : undefined}
            onConfigure={handleConfigure}
          />
        </div>
      </PageLayout>
      
      <SettingsSidebar
        isOpen={sidebarOpen}
        onClose={handleCloseSidebar}
        fileId={selectedFileId}
        fileName={selectedFileName}
      />
    </>
  )
}
