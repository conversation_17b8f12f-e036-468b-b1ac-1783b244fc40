import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Button, Form } from 'antd'
import { AuthLayout } from '../components/AuthLayout'
import { AuthCard } from '../components/AuthCard'
import { BrandHeader } from '../components/BrandHeader'
import { FormField } from '../components/FormField'
import { PrimaryButton } from '../components/PrimaryButton'

interface ForgotPasswordForm {
  email: string
}

export default function ForgotPassword() {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (values: ForgotPasswordForm) => {
    setLoading(true)
    try {
      console.log('Forgot password form values:', values)
    } catch (error) {
      console.error('Forgot password error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <AuthLayout>
      <AuthCard>
        <BrandHeader />

        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900 text-center">Reset Password</h2>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="space-y-4"
        >
          <FormField
            label="Email"
            name="email"
            type="email"
            placeholder="<EMAIL>"
            required
          />

          <div className="pt-4">
            <Form.Item>
              <PrimaryButton htmlType="submit" loading={loading}>
                Reset Password
              </PrimaryButton>
            </Form.Item>
          </div>

          <div className="pt-2">
            <Link to="/login">
              <Button
                className="w-full h-12 border-gray-200 hover:border-gray-300 rounded-lg font-medium text-gray-700"
              >
                Back to Login
              </Button>
            </Link>
          </div>
        </Form>

        <div className="mt-8 text-center">
          <span className="text-gray-600 text-sm">
            Don't have an account?{' '}
            <Link 
              to="/signup" 
              className="text-teal-600 hover:text-teal-700 font-medium"
            >
              Sign up
            </Link>
          </span>
        </div>
      </AuthCard>
    </AuthLayout>
  )
}