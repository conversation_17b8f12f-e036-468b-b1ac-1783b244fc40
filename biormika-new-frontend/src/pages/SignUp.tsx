import { useState } from 'react'
import { Form } from 'antd'
import { AuthLayout } from '../components/AuthLayout'
import { AuthCard } from '../components/AuthCard'
import { FormField } from '../components/FormField'
import { PrimaryButton } from '../components/PrimaryButton'

interface SignUpForm {
  fullName: string
  email: string
  password: string
  confirmPassword: string
}

export default function SignUp() {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (values: SignUpForm) => {
    setLoading(true)
    try {
      console.log('Sign up form values:', values)
    } catch (error) {
      console.error('Sign up error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <AuthLayout>
      <AuthCard width="lg">
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-8">Sign Up</h1>
        </div>

        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-900 mb-6">Account Details</h2>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="space-y-4"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              label="Full Name"
              name="fullName"
              placeholder="Full Name"
              required
              rules={[{ min: 2, message: 'Full name must be at least 2 characters!' }]}
            />

            <FormField
              label="Email"
              name="email"
              type="email"
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              label="Password"
              name="password"
              type="password"
              placeholder="Password"
              required
              rules={[{ min: 8, message: 'Password must be at least 8 characters!' }]}
            />

            <FormField
              label="Re-Type Password"
              name="confirmPassword"
              type="password"
              placeholder="Password"
              required
              dependencies={['password']}
              rules={[
                ({ getFieldValue }: any) => ({
                  validator(_: any, value: any) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve()
                    }
                    return Promise.reject(new Error('The passwords do not match!'))
                  },
                }),
              ]}
            />
          </div>

          <div className="pt-6">
            <Form.Item>
              <PrimaryButton htmlType="submit" loading={loading}>
                Create Account
              </PrimaryButton>
            </Form.Item>
          </div>
        </Form>
      </AuthCard>
    </AuthLayout>
  )
}