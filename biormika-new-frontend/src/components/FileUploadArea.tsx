import { useState } from 'react'
import { Upload, CheckCircle } from 'lucide-react'
import { toast } from 'sonner'
import { useAppDispatch, useAppSelector } from '../store'
import { addFiles, setIsUploading } from '../store/slices/filesSlice'
import { validateFiles, checkDuplicateFiles, MAX_FILE_SIZE } from '../utils/fileValidation'
import { brandColors } from '../constants/colors'
import { FileStatus } from '../types/files'

export function FileUploadArea() {
  const [isDragOver, setIsDragOver] = useState(false)
  const dispatch = useAppDispatch()
  const { files } = useAppSelector(state => state.files)

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleFilesUpload = (fileList: File[]) => {
    // Check for duplicates
    const { duplicates, unique } = checkDuplicateFiles(fileList, files)
    
    // Validate unique files
    const validFiles = validateFiles(unique)
    
    if (validFiles.length > 0) {
      dispatch(addFiles({ files: validFiles }))
      dispatch(setIsUploading(true))
      
      // Show success toast
      let message = `${validFiles.length} file${validFiles.length > 1 ? 's' : ''} uploaded`
      if (duplicates.length > 0) {
        message += `, ${duplicates.length} duplicate${duplicates.length > 1 ? 's' : ''} skipped`
      }
      toast.success(message)
    } else if (duplicates.length > 0) {
      // Only duplicates were selected
      toast.error(`${duplicates.length} duplicate file${duplicates.length > 1 ? 's' : ''} skipped`)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    const fileList = Array.from(e.dataTransfer.files)
    handleFilesUpload(fileList)
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const fileList = Array.from(e.target.files || [])
    handleFilesUpload(fileList)
    e.target.value = ''
  }

  const handleBrowseClick = () => {
    document.getElementById('file-input')?.click()
  }

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-16 text-center transition-colors
          ${isDragOver ? 'border-blue-400 bg-blue-50' : 'border-gray-300 bg-white'}
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          id="file-input"
          type="file"
          accept=".edf"
          multiple
          onChange={handleFileSelect}
          className="hidden"
        />
        
        <div className="flex flex-col items-center gap-4">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <Upload className="w-6 h-6 text-blue-600" />
          </div>
          
          <div className="space-y-2">
            <p className="text-gray-700 font-medium">
              {files.length > 0 ? 'Add more files by dragging & dropping or' : 'Drag & drop files or'}{' '}
              <button
                onClick={handleBrowseClick}
                className="text-blue-600 hover:text-blue-700 underline font-medium"
              >
                Browse
              </button>
            </p>
            {files.length > 0 && (
              <p className="text-sm text-blue-600">
                Adding to {files.length} existing file{files.length > 1 ? 's' : ''}
              </p>
            )}
          </div>
        </div>
      </div>
      
      <div className="mt-4 text-center">
        <p className="text-gray-600 text-sm leading-relaxed">
          File size max {MAX_FILE_SIZE / (1024 * 1024)}MB, format EDF (European Data Format) only,<br />
          file must be redacted with no patient-identifiable information.
        </p>
      </div>

      {/* Show existing files when files exist */}
      {files.length > 0 && (
        <div className="mt-8">
          <h3 className="text-lg font-semibold mb-4" style={{ color: brandColors.text.primary }}>
            Existing Files ({files.length})
          </h3>
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            {files.map((file, index) => (
              <div
                key={file.id}
                className={`flex items-center justify-between px-4 py-3 ${
                  index !== files.length - 1 ? 'border-b border-gray-100' : ''
                }`}
              >
                <div className="flex items-center gap-3 min-w-0 flex-1">
                  <div className="flex-shrink-0">
                    <CheckCircle className="w-5 h-5" style={{ color: brandColors.success }} />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p 
                      className="font-medium text-sm truncate" 
                      style={{ color: brandColors.text.primary }}
                      title={file.name}
                    >
                      {file.name}
                    </p>
                    <p className="text-xs" style={{ color: brandColors.text.secondary }}>
                      {formatFileSize(file.size)}
                    </p>
                  </div>
                </div>
                <div className="flex-shrink-0">
                  <span 
                    className="text-xs px-2 py-1 rounded-full" 
                    style={{ 
                      backgroundColor: file.status === FileStatus.VALID ? `${brandColors.success}20` : `${brandColors.primary}20`,
                      color: file.status === FileStatus.VALID ? brandColors.success : brandColors.primary
                    }}
                  >
                    {file.status === FileStatus.VALID ? 'Valid' : 'Processing'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}