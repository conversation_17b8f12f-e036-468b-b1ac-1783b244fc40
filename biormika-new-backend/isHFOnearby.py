def isHFOnearby(start_index, end_index, nearby_delay, start_ind_vec, end_ind_vec):
    """
    Check if there is another HFO nearby the HFO defined by start_index and end_index.
    
    Parameters:
    start_index (int): Start index of the HFO being analyzed.
    end_index (int): End index of the HFO being analyzed.
    nearby_delay (int): Allowed delay (in points) between nearby HFOs.
    start_ind_vec (list or numpy array): Vector of start indices of detected HFOs in another channel.
    end_ind_vec (list or numpy array): Vector of end indices of detected HFOs in another channel.

    Returns:
    result (int): Returns 1 if there is another HFO nearby, otherwise 0.
    """
    
    k = 0
    while k < len(start_ind_vec) and start_ind_vec[k] != 0:
        
        # Check for overlap or proximity between HFOs in different channels
        if (start_ind_vec[k] < start_index and end_ind_vec[k] > start_index):
            return 1
        elif (start_ind_vec[k] < end_index and end_ind_vec[k] > end_index):
            return 1
        elif (start_ind_vec[k] > start_index and end_ind_vec[k] < end_index):
            return 1
        elif (start_ind_vec[k] > end_index and start_ind_vec[k] <= end_index + nearby_delay):
            return 1
        elif (end_ind_vec[k] >= start_index - nearby_delay and end_ind_vec[k] < start_index):
            return 1
        elif (start_ind_vec[k] > end_index + nearby_delay):
            return 0  # No nearby HFOs, exit early
        
        k += 1

    return 0  # No nearby HFOs found
