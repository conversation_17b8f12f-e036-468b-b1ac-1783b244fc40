import sys
import os
import numpy as np
import re
import matplotlib.pyplot as plt
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from datetime import datetime, timed<PERSON><PERSON>


def extract_custom_sort_key(label,group_order):
    """
    Given a channel label (e.g., "A11-12"), extract a sort key.
    This example splits on '-' and then extracts the prefix and numeric part
    from the first section (e.g., "A11").
    """
    first_part = label.split('-')[0] 
    m = re.match(r"([A-Za-z]+)(\d+)", first_part)
    if m:
        prefix = m.group(1)
        number = int(m.group(2))
        # Use the group order from the dictionary (if not found >> large number so it sorts last)
        order = group_order.get(prefix, 1e9)
        return (order, number)
    return (1e9, 0)


def plotAndSaveHfo(output_dir, parameters, file_date, gui_output, header, locutoff, hicutoff, input_file_path, file_name):
	win_len2 = parameters.get('win_len2')
	stdHilbert = parameters.get('stdHilbert')
	meanHilbert = parameters.get('meanHilbert')
	RMS2 = parameters.get('RMS2')
	rejected_start_ind = parameters.get('rejected_start_ind')
	rejected_end_ind = parameters.get('rejected_end_ind')
	rejecLONG_start_ind = parameters.get('rejecLONG_start_ind')
	rejecLONG_end_ind = parameters.get('rejecLONG_end_ind')
	final_start_ind = parameters.get('final_start_ind')
	final_end_ind = parameters.get('final_end_ind')
	lfo_start_ind = parameters.get('lfo_start_ind')
	lfo_end_ind = parameters.get('lfo_end_ind')
	noise_start_ind = parameters.get('noise_start_ind')
	noise_end_ind = parameters.get('noise_end_ind')
	myEEG = parameters.get('myEEG')
	channel_labels = parameters.get('channel_labels')
	end_channel = parameters.get('end_channel')
	con_fact = parameters.get('con_fact')
	con_factN = parameters.get('con_factN')
	datalen_resulted_sec = parameters.get('datalen_resulted_sec')
	num_pts = parameters.get('num_pts')
	samp_freq = parameters.get('samp_freq')
	num_min = parameters.get('num_min')
	counter = parameters.get('counter')
	duration = parameters.get('duration')
	hfo_power = parameters.get('hfo_power')
	peak_freq = parameters.get('peak_freq')
	my_amp = parameters.get('my_amp')
	max_freq = parameters.get('max_freq')
	density = parameters.get('density')
	avg_duration = parameters.get('avg_duration')
	avg_hfo_power = parameters.get('avg_hfo_power')
	avg_peak_freq = parameters.get('avg_peak_freq')
	avg_my_amp = parameters.get('avg_my_amp')
	avg_max_freq = parameters.get('avg_max_freq')
	rejected = parameters.get('rejected')
	num_channels = parameters.get('num_channels')
	start_channel = parameters.get('start_channel')
	Schan = parameters.get('Schan')
	Echan = parameters.get('Echan')
	montage = parameters.get('montage')
	use_as_ref = parameters.get('use_as_ref')
	analysis_start = parameters.get('analysis_start')
	analysis_end = parameters.get('analysis_end')
	##
	plot_on = 1; 
	pt_label = 'Demo'; # set to any string, it will be printed in output file 
	file_descr = 'inter'; # will be used in output file3file_date = '20210721';
	# load_user_HFO = 0;
	analysis_epoch = 0.5; # epoch length in seconds for HFO analysis for calculating true positive rate, true negative rate, etc.


    # ------------------------------- Reorder channel data before printing output -------------------------------
	# Build a dictionary mapping each group prefix to its first appearance order.
	group_order = {}
	order = 0
	for label in channel_labels:
	    first_part = label.split('-')[0]
	    m = re.match(r"([A-Za-z]+)", first_part)
	    if m:
	        prefix = m.group(1)
	        if prefix not in group_order:
	            group_order[prefix] = order
	            order += 1

	# Build a list of tuples (channel_label, index) for channels from start_channel-1 to end_channel
	channel_data = [(channel_labels[i], i) for i in range(start_channel - 1, end_channel)]

	# Get sorted indices based on the custom sort key that uses group_order
	sorted_indices = [index for label, index in sorted(channel_data, key=lambda x: extract_custom_sort_key(x[0], group_order))]

	# Reorder channel-specific variables using sorted_indices.
	channel_labels = [channel_labels[i] for i in sorted_indices]
	counter = [counter[i] for i in sorted_indices]
	final_start_ind = [final_start_ind[i] for i in sorted_indices]
	final_end_ind = [final_end_ind[i] for i in sorted_indices]
	lfo_start_ind = [lfo_start_ind[i] for i in sorted_indices]
	lfo_end_ind = [lfo_end_ind[i] for i in sorted_indices]
	rejected_start_ind = [rejected_start_ind[i] for i in sorted_indices]
	rejected_end_ind = [rejected_end_ind[i] for i in sorted_indices]
	rejecLONG_start_ind = [rejecLONG_start_ind[i] for i in sorted_indices]
	rejecLONG_end_ind = [rejecLONG_end_ind[i] for i in sorted_indices]
	# If myEEG is a NumPy array with channels along axis 0:
	if isinstance(myEEG, np.ndarray):
	    myEEG = myEEG[sorted_indices, :]
 # ------------------------------- End of Reorder channel data -------------------------------

	# Define output filename
	current_time = datetime.now().strftime("%Y%m%d_%H%M%S")  # Format: YYYYMMDD_HHMMSS create unique file names for reports from same edf file
	report_filename = f"report_{file_name}_{current_time}.csv"
	file_path = os.path.join(output_dir, report_filename)
	# print(f"report csv file: {file_path}")
	if os.path.exists(file_path):
		raise FileExistsError(f"Unexpected: File already exists: {file_path}")
		
	# Open report files
	reportFile = open(file_path, 'w')

	# Check if patientID is anonymized or empty
	patient_id = header['patientID']
	if patient_id == "X X X X" or not re.search(r'\w+', patient_id):  # Adjust this check for other anonymized formats as needed
	    # Handle anonymized patient ID
	    patient_data = {
	        'patient_code': 'Anonymous',
	        'gender': 'Unknown',
	        'birth_date': 'Unknown',
	        'patient_name': 'Anonymous'
	    }
	else:
	    # Parse patient data using regex if it matches the expected format
	    match = re.match(r'(?P<patient_code>\w+) (?P<gender>[FMfm]) (?P<birth_date>\d\d-\w\w\w-\d\d\d\d) (?P<patient_name>\w+)', patient_id)
	    if match:
	        patient_data = match.groupdict()
	    else:
	        raise ValueError("patientID does not match the expected format.")

	if patient_data['birth_date'] not in ['Unknown', None, '']:
	    try:
	        birth_date = datetime.strptime(patient_data['birth_date'], '%d-%b-%Y')

	        # Ensure birth_date makes sense
	        if birth_date > datetime.now():
	            raise ValueError(f"Birth date {birth_date} pass the current time")

	        # Calculate age accurately
	        from dateutil.relativedelta import relativedelta
	        age = relativedelta(datetime.now(), birth_date).years
	    except ValueError:
	        raise ValueError(f"Invalid birth_date format: {patient_data['birth_date']}")
	else:
	    age = 'Unknown'

	# Get number of implanted contacts
	implanted_contacts = np.sum([label != 'EDFANNOTATIONS' for label in header['label']])

	# Calculate segment start and end times
	starttime = datetime.strptime(header['startdate'] + " " + header['starttime'], "%d.%m.%y %H.%M.%S")


	if analysis_start is None:
		raise ValueError("analysis_start is missing or None")
	if analysis_end is None:
		raise ValueError("analysis_end is missing or None")

	segment_start = starttime + timedelta(seconds=analysis_start)
	segment_end = starttime + timedelta(seconds=analysis_end)
	segment_length = parameters.get("segment_length")



	# Write demographics to report file
	reportFile.write('Demographics\n')
	reportFile.write(f"Patient name: {patient_data['patient_name']} | MRN Hospital: {patient_data['patient_code']}\n")
	reportFile.write(f"DOB: {patient_data['birth_date']} | Age (Years): {age} | Gender: {patient_data['gender']}\n")

	# Write recording parameters to report file
	reportFile.write('Recording parameters\n')
	reportFile.write(f"Sampling rate (Hz): {header['srate']} | Num of implanted contacts: {implanted_contacts}\n")

	# Write analyzed segment to report file
	reportFile.write('Analyzed segment\n')
	reportFile.write(f"File name: {file_name}\n")
	reportFile.write(f"Date: {segment_start:%Y.%m.%d} | Start time: {segment_start:%H:%M:%S} | End time: {segment_end:%H:%M:%S} | Length (sec): {segment_length:.2f}\n")
	reportFile.write(f"Montage: {montage} | Reference: {use_as_ref} | Patient state: \n")
	reportFile.write(f"Num. channels analyzed: {num_channels} | Frequency band: {locutoff}-{hicutoff} Hz\n")
	reportFile.write(f"Total num. of HFOs: {np.sum(counter)} | Total num. of LFOs (rejected HFOs): {rejected} |\n\n")

	# HFO characteristics
	reportFile.write('----------------------------HFO profiles: numerical---------------------------\n')


	# Header with fixed-width columns
	header = "| {:<5} | {:<15} | {:<8} | {:<8} | {:<8} | {:<8} | {:<8} | {:<8} | {:<8} | {:<8} |".format(
	    "Ch#", "ChName", "Rate", "Dens", "Conn", "Durn", "PkFq", "LogPwr", "AvgAmp", "MaxFq"
	)
	separator = "-" * len(header)

	reportFile.write(header + "\n")
	reportFile.write(separator + "\n")  # Add a separator line

	# Iterate over each channel and write data
	for ch in range(start_channel - 1, end_channel):
	    ch_name = channel_labels[ch]
	    line = "| {:<5} | {:<15} | {:<8.0f} | {:<8.0f} | {:<8.1f} | {:<8.0f} | {:<8.0f} | {:<8.1f} | {:<8.0f} | {:<8.0f} |".format(
	        ch + 1,                    # Channel number
	        ch_name,                   # Channel name
	        counter[ch] / num_min,     # Rate
	        density[ch] / num_min,     # Dens
	        con_factN[ch],             # Conn
	        avg_duration[ch],          # Durn
	        avg_peak_freq[ch],         # PkFq
	        avg_hfo_power[ch],         # LogPwr
	        avg_my_amp[ch],            # AvgAmp
	        avg_max_freq[ch],          # MaxFq
	    )
	    reportFile.write(line + "\n")


	# Close files
	reportFile.close()



	# Display summary in GUI or console
	msg = f"Length of data analyzed: {num_pts} data points; {num_pts / samp_freq:.4f} sec; {num_min:.4f} min"
	if gui_output:
	    gui_output(msg)
	else:
	    print(msg)

	msg = f"Total number of HFOs: {np.sum(counter)}"
	if gui_output:
	    gui_output(msg)
	else:
	    print(msg)

	msg = f"Total number of LFOs (rejected HFOs): {rejected}"
	if gui_output:
	    gui_output(msg)
	else:
	    print(msg)

	if gui_output:
		gui_output("** HFO characteristics **")
	else:
		print("*" * 80)

	if gui_output:
		gui_output('Channel\t\tRate\tDens\tConn\tDurn\tPkFq\tLogPwr\tMeanAmp\tMaxFq\n')
	else:
		print('Channel\t\tRate\tDens\tConn\tDurn\tPkFq\tLogPwr\tMeanAmp\tMaxFq\n')
	# Iterate over channels and write their data
	for i in range(start_channel - 1,end_channel):
		tmpch = channel_labels[i]  # Get channel label
		tmpr = tmpch.find('-')  # Find the separator index
		if tmpr != -1:
			tmpch1 = tmpch[:tmpr]
			tmpch2 = tmpch[tmpr + 1:]
		else:
			tmpch1,tmpch2 = tmpch, ""
		tmpch1 = tmpch[:tmpr]  # First part of the label
		tmpch2 = tmpch[tmpr + 1:]  # Second part of the label

		# Find non-zero elements in each array

		nonzero_dur = np.nonzero(np.array(duration[i]))[0]
		nonzero_PF = np.nonzero(np.array(peak_freq[i]))[0]
		nonzero_PP = np.nonzero(np.array(hfo_power[i]))[0]
		nonzero_MA = np.nonzero(np.array(my_amp[i]))[0]
		nonzero_MF = np.nonzero(np.array(max_freq[i]))[0]


		# Convert the list to a NumPy array
		duration_array = np.array(duration[i])
		peak_freq_array = np.array(peak_freq[i])
		hfo_power_array = np.array(hfo_power[i])
		my_amp_array = np.array(my_amp[i])
		max_freq_array = np.array(max_freq[i])

		# Calculate means for non-zero elements
		mean_dur = np.mean(duration_array[nonzero_dur]) if len(nonzero_dur) > 0 else 0
		mean_PF = np.mean(peak_freq_array[nonzero_PF]) if len(nonzero_PF) > 0 else 0
		mean_PP = np.mean(np.log10(hfo_power_array[nonzero_PP])) if len(nonzero_PP) > 0 else 0
		mean_MA = np.mean(my_amp_array[nonzero_MA]) if len(nonzero_MA) > 0 else 0
		mean_MF = np.mean(max_freq_array[nonzero_MF]) if len(nonzero_MF) > 0 else 0

		line = f"{i + 1}    {tmpch}\t{counter[i] / num_min:.0f}\t{density[i] / num_min:.0f}\t{con_factN[i]:.1f}\t"
		line += f"{mean_dur:.0f}\t{mean_PF:.0f}\t{mean_PP:.1f}\t{mean_MA:.0f}\t{mean_MF:.0f}\n"

		if gui_output:
			gui_output(line)
		else:
			print(line)

	if gui_output:
		line = f"Data saved to:{file_path}"
		gui_output(line)
	print(line)

	# Check whether plotting is enabled
	if plot_on == 1:
	    # Initialize empty times for HFOs if no user input
		hfo_ch_t = np.zeros(len(channel_labels))

		return (hfo_ch_t, Schan, Echan, num_pts, myEEG, final_start_ind, final_end_ind, 
		               lfo_start_ind, lfo_end_ind, rejected_start_ind, rejected_end_ind, 
		               rejecLONG_start_ind, rejecLONG_end_ind, channel_labels, counter, 
		               num_min, samp_freq, analysis_epoch, RMS2, meanHilbert, stdHilbert, 
		               win_len2, input_file_path, file_path,report_filename,locutoff,hicutoff)

