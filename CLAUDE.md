# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Frontend (biormika-new-frontend)
- `npm run dev` - Start development server with Vite
- `npm run build` - Build for production (TypeScript compilation + Vite build)
- `npm run lint` - Run ESLint for code quality checks
- `npm run preview` - Preview production build locally

### Backend (Biormika-AWS-Backend)
- `npm run offline` - Run serverless offline (API: http://localhost:3001, WebSocket: ws://localhost:3002)
- `serverless deploy --stage dev` - Deploy to AWS development environment
- `python -m pytest tests/` - Run backend tests
- `pip install -r requirements.txt` - Install Python dependencies
- `npm install` - Install Node.js dependencies

## Architecture Overview

This is a serverless EEG/HFO (High-Frequency Oscillation) detection platform built with AWS services and React. The system was migrated from a desktop application (`biormika-old-backend/`) to a serverless architecture while preserving the core algorithms and business logic.

### Backend Architecture (AWS Serverless)

The backend is a serverless architecture using AWS Lambda functions orchestrated by the Serverless Framework:

- **AWS Lambda**: All API endpoints and processing tasks run as Lambda functions
- **API Gateway**: REST API endpoints and WebSocket connections for real-time updates
- **S3**: Storage for uploaded EDF files and analysis results
- **DynamoDB**: Metadata storage for users, files, and analysis records
- **Cognito**: User authentication and JWT token management
- **Step Functions**: Orchestration of the multi-step analysis workflow
- **CloudWatch**: Centralized logging and monitoring

#### Core Processing Flow
1. User uploads EDF file → S3 (via presigned URL)
2. File validation Lambda triggered
3. Analysis initiated via Step Functions
4. HFO detection algorithm processes EDF data
5. Results stored in S3, metadata in DynamoDB
6. WebSocket updates sent during processing
7. PDF report generated with visualizations

#### Key Backend Modules
- `analysis/core/hfo_analysis.py` - Core HFO detection algorithm
- `analysis/edf/reader.py` - EDF file format parsing
- `auth/` - JWT authentication and authorization
- `files/` - File upload and validation handlers
- `websocket/` - Real-time progress updates
- `visualization/` - PDF report and plot generation
- `storage/` - S3 and DynamoDB utilities

### Legacy Backend (biormika-old-backend) - Reference Implementation

The original desktop application contains the **proven algorithms** that the AWS backend must replicate. This is the working reference implementation:

#### Core Algorithm Files
- `hfo_analysis.py` - **Master HFO detection algorithm** (Threshold Scheme 10)
- `calculate_HFO_characteristics.py` - HFO spectral analysis and validation
- `pyedfreader.py` - EDF file binary reading and signal extraction
- `plotAndSaveHfoV2.py` - Visualization and statistical reporting
- `find_blanks.py` - Data discontinuity detection and removal

#### Critical Business Logic (Must Be Preserved)

**HFO Detection Pipeline (Threshold Scheme 10):**
1. **Data Preprocessing**: Load EDF → Apply montage → Filter (notch 60Hz + bandpass)
2. **Energy Signal**: Calculate Hilbert transform → Generate RMS2 energy signal
3. **Statistical Thresholding**: `threshold = meanHilbert + (multiplier * stdHilbert)`
4. **HFO Detection**: Find segments where energy > threshold for ≥10ms duration
5. **Multi-stage Validation**:
   - Peak count validation (≥6 peaks above amplitude1, ≥3 above amplitude2)
   - Frequency validation (≥70 Hz required for HFO classification)
   - Power ratio validation (HFO range vs conventional range)
6. **Connectivity Analysis**: Spatial-temporal synchronization between channels
7. **Report Generation**: Calculate rates, densities, and connectivity metrics

**Key Parameters (Default Values):**
- Amplitude thresholds: 5 STD (amplitude1), 3 STD (amplitude2)
- Peak requirements: 6 peaks (threshold1), 3 peaks (threshold2)
- Minimum duration: 10ms
- Frequency threshold: 70 Hz minimum
- Temporal sync window: 10ms
- Spatial sync window: 10ms

**Critical Processing Steps:**
1. **Filtering Pipeline**: 6th-order Butterworth bandpass (50-300 Hz typical)
2. **Blank Detection**: Remove data discontinuities and artifacts
3. **Energy Statistics**: Calculate mean and STD of Hilbert-transformed signal
4. **HFO Characterization**: FFT analysis with Hamming window and zero-padding
5. **Validation Chain**: Energy → Duration → Peaks → Frequency → Power ratio

**AWS Implementation Note**: The serverless backend should replicate this exact algorithm flow, parameters, and validation logic to ensure consistent results with the proven desktop version.

### Frontend Architecture

The frontend is a modern React SPA with enterprise-grade state management:

- **React 19 + TypeScript** - Type-safe component development
- **Vite** - Fast build tooling and HMR
- **Redux Toolkit** - Centralized state management with persistence
- **React Router v7** - Declarative routing with auth guards
- **Ant Design + Tailwind CSS** - Hybrid styling approach
- **Axios** - HTTP client with interceptors

#### State Management Philosophy
- All persistent/shared state managed through Redux
- File uploads, auth state, and analysis settings in Redux slices
- Local component state only for UI-specific concerns
- Redux Persist for localStorage persistence

#### Routing Architecture
- Declarative route configuration in `src/router/routes.ts`
- Automatic auth guard application based on route flags
- Protected routes redirect to login with return URL preservation
- Guest-only routes (login/signup) redirect authenticated users

### API Integration

#### Authentication Flow
1. Login/signup returns JWT tokens (access + refresh)
2. Tokens stored in Redux and persisted to localStorage
3. Axios interceptor adds Authorization header
4. 401 responses trigger token refresh automatically
5. Failed refresh redirects to login

#### File Upload Flow
1. Initiate upload to get presigned S3 URL
2. Direct upload to S3 from browser
3. Validate file format on backend
4. Real-time status updates via Redux

#### Analysis Configuration
```typescript
interface AnalysisSettings {
  thresholdSettings: {
    amplitude1: number  // Default: 5
    amplitude2: number  // Default: 3
    peaks1: number      // Default: 6
    peaks2: number      // Default: 3
    duration: number    // Default: 10ms
  }
  frequencyFilterSettings: {
    lowCutoffFilter: number   // 80Hz for ripples
    highCutoffFilter: number  // 250Hz for ripples, 500Hz for fast ripples
  }
  montageType: 'bipolar' | 'average' | 'referential'
}
```

### Key Development Patterns

#### Component Structure
- Functional components with TypeScript interfaces
- Consistent props pattern with proper typing
- Error boundaries wrap major sections
- Custom hooks for complex logic

#### State Access Pattern
```typescript
// Always use typed hooks
const dispatch = useAppDispatch()
const { data, isLoading } = useAppSelector(state => state.slice)
```

#### Error Handling
- Global error boundaries for crash protection
- Toast notifications for user feedback
- Structured error responses from API
- Graceful degradation on failures

#### Security Considerations
- JWT tokens with short expiration
- Row-level security in DynamoDB
- Presigned URLs expire after upload
- API request validation
- CORS configuration per environment

### Testing Approach

#### Backend Testing
- Python pytest for Lambda function tests
- Mock AWS services with moto library
- Integration tests for Step Functions
- Unit tests for HFO algorithm

#### Frontend Testing
- Check with team for preferred testing framework
- Component testing likely with React Testing Library
- E2E tests potentially with Cypress/Playwright

### Deployment

#### Backend Deployment
```bash
# Deploy to development
serverless deploy --stage dev

# Deploy to production
serverless deploy --stage prod
```

#### Frontend Deployment
```bash
# Build for production
npm run build

# Deploy script handles S3 upload and CloudFront invalidation
node deploy.js
```

### Environment Configuration

#### Backend (.env.dev)
- AWS credentials and region
- Cognito pool IDs
- S3 bucket names
- DynamoDB table names
- WebSocket endpoint

#### Frontend (.env)
- API endpoint URLs
- WebSocket URL
- Cognito client ID
- Environment name

### Common Development Tasks

#### Adding a New API Endpoint
1. Define Lambda handler in appropriate module
2. Add function configuration to serverless.yml
3. Update API documentation
4. Add TypeScript types in frontend
5. Create Redux actions if stateful
6. Update Axios service layer

#### Modifying HFO Algorithm
1. Core logic in `analysis/core/hfo_analysis.py`
2. Update threshold validation ranges
3. Test with sample EDF files
4. Update visualization if needed
5. Document parameter changes

#### Adding New File Validations
1. Update `files/validators.py`
2. Add specific format checks
3. Update error messages
4. Test with edge cases
5. Update frontend validation

### Troubleshooting

#### Common Issues
- **CORS errors**: Check serverless.yml CORS configuration
- **WebSocket disconnects**: Verify WebSocket URL and auth
- **File upload fails**: Check S3 bucket permissions and CORS
- **Analysis stuck**: Check Step Functions execution in AWS console
- **Auth errors**: Verify Cognito configuration and token expiry

#### Debugging Tools
- CloudWatch Logs for Lambda execution
- Step Functions visual workflow
- DynamoDB table explorer
- S3 bucket browser
- Redux DevTools for state inspection

### Performance Considerations

#### Backend Optimization
- Lambda cold start mitigation with provisioned concurrency
- DynamoDB on-demand scaling
- S3 transfer acceleration for large files
- Step Functions express workflows for speed

#### Frontend Optimization
- Code splitting with React.lazy
- Redux state normalization
- Virtual scrolling for large file lists
- Image optimization in PDF reports
- Memoization for expensive computations