# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a monorepo containing the Biormika HFO (High-Frequency Oscillations) Detection system, which consists of a React frontend and a Python backend. The system analyzes EEG data from EDF files to detect HFO patterns.

## Repository Structure

```
main/
├── biormika-new-frontend/     # React + TypeScript frontend application
│   ├── src/                   # Frontend source code
│   ├── package.json          # Frontend dependencies and scripts
│   └── CLAUDE.md             # Detailed frontend guidance (see below)
└── biormika-new-backend/      # Python backend for HFO analysis
    ├── first_version_UI_V2.4.py  # PyQt5 desktop application
    ├── hfo_analysis.py           # Core HFO detection algorithm
    ├── calculate_HFO_characteristics.py
    ├── plotAndSaveHfoV2.py       # HFO visualization
    └── pyedfreader.py            # EDF file reader
```

## Development Commands

### Frontend (React + TypeScript)
Navigate to `biormika-new-frontend/` first:

```bash
cd biormika-new-frontend
npm install              # Install dependencies
npm run dev             # Start development server with Vite
npm run build           # Build for production (TypeScript + Vite)
npm run lint            # Run ESLint for code quality
npm run preview         # Preview production build locally
```

### Backend (Python)
Navigate to `biormika-new-backend/` first:

```bash
cd biormika-new-backend
python first_version_UI_V2.4.py    # Run the PyQt5 desktop application
```

Note: There is no requirements.txt file currently. Dependencies need to be manually installed:
- PyQt5
- numpy
- scipy
- matplotlib
- fpdf
- PIL (Pillow)

## Architecture Overview

### Frontend Architecture
The frontend is a modern React application with comprehensive state management. For detailed frontend architecture, refer to `biormika-new-frontend/CLAUDE.md` which contains:
- Detailed tech stack (React 19, TypeScript, Vite, Redux Toolkit, Ant Design, TailwindCSS)
- Component patterns and organization
- State management with Redux
- Routing with React Router v7 and auth guards
- Styling standards and brand color system
- Form development patterns
- TypeScript best practices

### Backend Architecture
The backend consists of:

1. **PyQt5 Desktop Application** (`first_version_UI_V2.4.py`)
   - Main GUI application for HFO detection
   - File browser for EDF files
   - Configuration interface for analysis parameters
   - Results visualization

2. **Core Analysis Module** (`hfo_analysis.py`)
   - `run_hfo_algorithm()`: Main HFO detection algorithm
   - Signal processing with scipy (filtering, Hilbert transform)
   - HFO detection based on configurable thresholds

3. **Supporting Modules**
   - `calculate_HFO_characteristics.py`: Computes HFO properties (duration, frequency, amplitude)
   - `find_blanks.py`: Identifies artifact/blank segments in EEG data
   - `isHFOnearby.py`: Checks proximity of HFO events
   - `plotAndSaveHfoV2.py`: Generates visualization plots
   - `pyedfreader.py`: Reads EDF (European Data Format) files

## Key Patterns and Conventions

### Frontend Patterns
Refer to `biormika-new-frontend/CLAUDE.md` for comprehensive frontend patterns including:
- Component development patterns
- Redux state management
- Form handling with Ant Design
- Color system usage (NEVER hardcode colors)
- TypeScript interfaces and naming conventions
- Error handling with boundaries and toast notifications

### Backend Patterns

#### Signal Processing Flow
1. Load EDF file using `pyedfreader`
2. Apply montage (bipolar, average, or referential)
3. Filter signals (notch filter at 60Hz, bandpass 80-250Hz for ripples, 250-500Hz for fast ripples)
4. Detect HFOs using envelope detection with Hilbert transform
5. Apply threshold-based detection (customizable thresholds)
6. Calculate HFO characteristics
7. Generate visualizations and reports

#### PyQt5 GUI Patterns
- Main window inherits from `QMainWindow`
- Layouts use `QVBoxLayout`, `QHBoxLayout`, `QGridLayout`
- Dynamic UI elements based on loaded EDF file channels
- Progress dialogs for long-running operations
- File dialogs for EDF file selection

## Important Considerations

### EDF File Handling
- The system processes EDF (European Data Format) files containing EEG data
- Supports multiple channels with different sampling rates
- Handles large files with progress indication
- Validates file format before processing

### HFO Detection Parameters
The algorithm uses configurable thresholds:
- Energy threshold (2-10x baseline)
- Duration threshold (minimum HFO duration)
- Frequency band selection (ripples vs fast ripples)
- Artifact rejection parameters

### Performance Considerations
- Large EDF files can take significant time to process
- Signal filtering is computationally intensive
- Visualization generation may require memory optimization
- Consider chunked processing for very large files

## Testing Approach
Currently, there are no automated tests configured. When implementing tests:
- Frontend: Consider Jest + React Testing Library
- Backend: Use pytest for Python unit tests
- Test signal processing algorithms with known datasets
- Validate HFO detection accuracy against ground truth

## Common Development Tasks

### Adding New Analysis Features
1. Modify `hfo_analysis.py` to add new detection algorithms
2. Update GUI in `first_version_UI_V2.4.py` for new parameters
3. Add corresponding frontend UI if web interface is used

### Modifying Detection Thresholds
1. Update threshold options in PyQt5 GUI
2. Modify `run_hfo_algorithm()` to accept new parameters
3. Document threshold effects on detection sensitivity

### Extending File Format Support
1. Create new reader module similar to `pyedfreader.py`
2. Update file browser to accept new formats
3. Ensure signal data structure consistency

## Deployment Considerations

### Frontend Deployment
- Build with `npm run build`
- Deploy dist/ folder to web server
- Configure API endpoints for backend communication

### Backend Deployment
- PyQt5 application requires desktop environment
- Consider PyInstaller for standalone executable
- Ensure all Python dependencies are included

## Integration Points
Currently, the frontend and backend appear to be separate systems. Future integration might involve:
- REST API for backend HFO analysis service
- WebSocket for real-time analysis updates
- File upload endpoint for EDF files
- Results download API

When implementing integration:
1. Create API endpoints in backend (consider Flask/FastAPI)
2. Update frontend to use axios for API calls
3. Implement file upload with progress tracking
4. Add authentication if needed